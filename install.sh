#!/bin/bash

# Matrix Synapse 快速安装脚本
# 这是一个简化的安装脚本，用于快速部署Matrix Synapse

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    Matrix Synapse 快速安装脚本"
    echo "=================================================="
    echo -e "${NC}"
    echo "这个脚本将帮助您快速安装Matrix Synapse"
    echo ""
    echo "安装选项:"
    echo "  1. 完整自动化安装 (推荐)"
    echo "  2. 仅下载文件，手动配置"
    echo "  3. 查看系统要求"
    echo "  4. 退出"
    echo ""
}

# 检查系统要求
check_requirements() {
    echo "系统要求检查:"
    echo "=================================="
    
    # 检查操作系统
    if [[ -f /etc/debian_version ]]; then
        local version=$(cat /etc/debian_version | cut -d. -f1)
        if [[ $version -ge 12 ]]; then
            echo "✓ 操作系统: Debian $version (支持)"
        else
            echo "✗ 操作系统: Debian $version (建议使用Debian 12+)"
        fi
    else
        echo "✗ 操作系统: 非Debian系统 (不推荐)"
    fi
    
    # 检查内存
    local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $mem_gb -ge 8 ]]; then
        echo "✓ 内存: ${mem_gb}GB (充足)"
    elif [[ $mem_gb -ge 4 ]]; then
        echo "⚠ 内存: ${mem_gb}GB (最低要求，建议8GB+)"
    else
        echo "✗ 内存: ${mem_gb}GB (不足，至少需要4GB)"
    fi
    
    # 检查磁盘空间
    local disk_gb=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $disk_gb -ge 50 ]]; then
        echo "✓ 磁盘空间: ${disk_gb}GB 可用 (充足)"
    elif [[ $disk_gb -ge 20 ]]; then
        echo "⚠ 磁盘空间: ${disk_gb}GB 可用 (最低要求)"
    else
        echo "✗ 磁盘空间: ${disk_gb}GB 可用 (不足，至少需要20GB)"
    fi
    
    # 检查网络
    if ping -c 1 8.8.8.8 > /dev/null 2>&1; then
        echo "✓ 网络连接: 正常"
    else
        echo "✗ 网络连接: 异常"
    fi
    
    # 检查权限
    if [[ $EUID -eq 0 ]]; then
        echo "✓ 权限: root用户"
    else
        echo "✗ 权限: 需要root权限运行"
    fi
    
    echo "=================================="
    echo ""
}

# 下载项目文件
download_files() {
    log_info "下载Matrix部署文件..."
    
    # 这里应该是实际的下载地址
    # 目前使用本地文件作为示例
    
    if [[ ! -f "deploy.sh" ]]; then
        log_error "部署文件不存在，请确保在正确的目录中运行此脚本"
        return 1
    fi
    
    log_info "文件检查完成"
}

# 运行完整安装
run_full_install() {
    log_info "开始完整自动化安装..."
    
    # 检查部署脚本
    if [[ ! -f "deploy.sh" ]]; then
        log_error "deploy.sh不存在"
        return 1
    fi
    
    # 运行部署脚本
    chmod +x deploy.sh
    ./deploy.sh
}

# 显示手动配置说明
show_manual_instructions() {
    echo ""
    log_info "手动配置说明:"
    echo "=================================="
    echo "1. 确保所有文件已下载到当前目录"
    echo "2. 编辑配置文件以适应您的环境:"
    echo "   - 域名设置"
    echo "   - Cloudflare API Token"
    echo "   - 端口配置"
    echo "   - RouterOS设置 (可选)"
    echo ""
    echo "3. 运行部署脚本:"
    echo "   sudo ./deploy.sh"
    echo ""
    echo "4. 详细说明请参考:"
    echo "   - README.md: 项目概述"
    echo "   - manual-guide.md: 详细手动部署指南"
    echo "=================================="
    echo ""
}

# 主菜单
main_menu() {
    while true; do
        show_welcome
        read -p "请选择操作 (1-4): " choice
        
        case $choice in
            1)
                echo ""
                log_info "选择: 完整自动化安装"
                check_requirements
                read -p "是否继续安装? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    download_files
                    run_full_install
                    break
                fi
                ;;
            2)
                echo ""
                log_info "选择: 仅下载文件"
                download_files
                show_manual_instructions
                break
                ;;
            3)
                echo ""
                check_requirements
                read -p "按Enter键返回主菜单..." -r
                ;;
            4)
                echo ""
                log_info "退出安装"
                exit 0
                ;;
            *)
                echo ""
                log_error "无效选择，请输入1-4"
                sleep 2
                ;;
        esac
    done
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 主函数
main() {
    # 如果有命令行参数，直接执行对应操作
    case "${1:-}" in
        --check)
            check_requirements
            ;;
        --auto)
            check_root
            download_files
            run_full_install
            ;;
        --help|-h)
            echo "Matrix Synapse 快速安装脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --auto      自动安装 (需要root权限)"
            echo "  --check     检查系统要求"
            echo "  --help      显示此帮助信息"
            echo ""
            echo "无参数运行将显示交互式菜单"
            ;;
        "")
            main_menu
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
