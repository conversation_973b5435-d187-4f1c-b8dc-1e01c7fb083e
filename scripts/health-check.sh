#!/bin/bash

# Matrix Synapse 健康检查脚本
# 检查服务状态、端口连通性、证书有效期、磁盘空间等
# 支持多种通知方式和详细的状态报告

# 版本: 1.0.0
# 日期: 2025-07-13

set -e

# 配置变量
INSTALL_PATH="${INSTALL_PATH:-/opt/matrix}"
LOG_FILE="$INSTALL_PATH/logs/health-check.log"
CONFIG_FILE="${CONFIG_FILE:-$INSTALL_PATH/configs/health-check.conf}"

# 检查阈值
DISK_USAGE_THRESHOLD="${DISK_USAGE_THRESHOLD:-80}"
MEMORY_USAGE_THRESHOLD="${MEMORY_USAGE_THRESHOLD:-85}"
CERT_EXPIRY_DAYS="${CERT_EXPIRY_DAYS:-30}"
LOAD_AVERAGE_THRESHOLD="${LOAD_AVERAGE_THRESHOLD:-2.0}"

# 通知配置
NOTIFICATION_ENABLED="${NOTIFICATION_ENABLED:-false}"
WEBHOOK_URL="${WEBHOOK_URL:-}"
EMAIL_TO="${EMAIL_TO:-}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 状态变量
OVERALL_STATUS="OK"
ISSUES_FOUND=()
WARNINGS_FOUND=()

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warn() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
    WARNINGS_FOUND+=("$1")
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
    ISSUES_FOUND+=("$1")
    OVERALL_STATUS="CRITICAL"
}

log_debug() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG] $1"
    echo -e "${BLUE}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# 检查Docker服务状态
check_docker_services() {
    log_info "检查Docker服务状态..."
    
    local services=("matrix-synapse" "matrix-coturn" "matrix-nginx")
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$service$"; then
            local status=$(docker inspect --format='{{.State.Status}}' "$service" 2>/dev/null)
            if [[ "$status" == "running" ]]; then
                log_info "服务 $service: 运行中"
            else
                log_error "服务 $service: 状态异常 ($status)"
            fi
        else
            log_error "服务 $service: 未运行"
        fi
    done
}

# 检查端口连通性
check_port_connectivity() {
    log_info "检查端口连通性..."
    
    local ports=("8008:Synapse HTTP" "8443:HTTPS" "3478:Coturn STUN/TURN")
    
    for port_info in "${ports[@]}"; do
        local port=$(echo "$port_info" | cut -d: -f1)
        local service=$(echo "$port_info" | cut -d: -f2)
        
        if netstat -tuln | grep -q ":$port "; then
            log_info "端口 $port ($service): 监听中"
        else
            log_error "端口 $port ($service): 未监听"
        fi
    done
    
    # 检查外部连通性
    local domain=$(grep "server_name:" "$INSTALL_PATH/configs/homeserver.yaml" | awk '{print $2}' | tr -d '"' 2>/dev/null || echo "localhost")
    
    if curl -s -o /dev/null -w "%{http_code}" "https://$domain:8443/_matrix/client/versions" | grep -q "200"; then
        log_info "Matrix API: 可访问"
    else
        log_error "Matrix API: 无法访问"
    fi
}

# 检查SSL证书
check_ssl_certificates() {
    log_info "检查SSL证书..."
    
    local cert_file="$INSTALL_PATH/certs/fullchain.pem"
    
    if [[ -f "$cert_file" ]]; then
        local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [[ $days_until_expiry -lt 0 ]]; then
            log_error "SSL证书已过期"
        elif [[ $days_until_expiry -lt $CERT_EXPIRY_DAYS ]]; then
            log_warn "SSL证书将在 $days_until_expiry 天后过期"
        else
            log_info "SSL证书有效，还有 $days_until_expiry 天过期"
        fi
    else
        log_error "SSL证书文件不存在: $cert_file"
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    local usage=$(df "$INSTALL_PATH" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $usage -gt $DISK_USAGE_THRESHOLD ]]; then
        log_error "磁盘使用率过高: ${usage}%"
    elif [[ $usage -gt $((DISK_USAGE_THRESHOLD - 10)) ]]; then
        log_warn "磁盘使用率较高: ${usage}%"
    else
        log_info "磁盘使用率正常: ${usage}%"
    fi
    
    # 检查数据库大小
    local db_file="$INSTALL_PATH/data/homeserver.db"
    if [[ -f "$db_file" ]]; then
        local db_size=$(du -sh "$db_file" | cut -f1)
        log_info "数据库大小: $db_size"
    fi
}

# 检查内存使用
check_memory_usage() {
    log_info "检查内存使用..."
    
    local memory_info=$(free | grep Mem)
    local total=$(echo "$memory_info" | awk '{print $2}')
    local used=$(echo "$memory_info" | awk '{print $3}')
    local usage=$((used * 100 / total))
    
    if [[ $usage -gt $MEMORY_USAGE_THRESHOLD ]]; then
        log_error "内存使用率过高: ${usage}%"
    elif [[ $usage -gt $((MEMORY_USAGE_THRESHOLD - 10)) ]]; then
        log_warn "内存使用率较高: ${usage}%"
    else
        log_info "内存使用率正常: ${usage}%"
    fi
    
    # 检查Docker容器内存使用
    for container in matrix-synapse matrix-coturn matrix-nginx; do
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            local mem_usage=$(docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" "$container" | tail -n 1 | awk '{print $2}')
            log_info "容器 $container 内存使用: $mem_usage"
        fi
    done
}

# 检查系统负载
check_system_load() {
    log_info "检查系统负载..."
    
    local load_1min=$(uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs)
    local cpu_cores=$(nproc)
    
    if (( $(echo "$load_1min > $LOAD_AVERAGE_THRESHOLD" | bc -l) )); then
        log_error "系统负载过高: $load_1min (CPU核心数: $cpu_cores)"
    else
        log_info "系统负载正常: $load_1min (CPU核心数: $cpu_cores)"
    fi
}

# 检查数据库状态
check_database_status() {
    log_info "检查数据库状态..."
    
    local db_file="$INSTALL_PATH/data/homeserver.db"
    
    if [[ -f "$db_file" ]]; then
        # 检查数据库完整性
        if sqlite3 "$db_file" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_info "数据库完整性检查通过"
        else
            log_error "数据库完整性检查失败"
        fi
        
        # 检查数据库连接
        if docker exec matrix-synapse python -c "
import sqlite3
try:
    conn = sqlite3.connect('/data/homeserver.db')
    conn.execute('SELECT 1')
    conn.close()
    print('OK')
except Exception as e:
    print(f'ERROR: {e}')
" | grep -q "OK"; then
            log_info "数据库连接正常"
        else
            log_error "数据库连接失败"
        fi
    else
        log_error "数据库文件不存在: $db_file"
    fi
}

# 检查日志错误
check_log_errors() {
    log_info "检查日志错误..."
    
    local log_files=(
        "$INSTALL_PATH/logs/homeserver.log"
        "$INSTALL_PATH/logs/nginx/error.log"
        "$INSTALL_PATH/logs/coturn/turnserver.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            local error_count=$(grep -c "ERROR\|CRITICAL" "$log_file" 2>/dev/null || echo 0)
            local recent_errors=$(tail -n 100 "$log_file" | grep -c "ERROR\|CRITICAL" 2>/dev/null || echo 0)
            
            if [[ $recent_errors -gt 10 ]]; then
                log_error "日志文件 $(basename $log_file) 最近有 $recent_errors 个错误"
            elif [[ $recent_errors -gt 0 ]]; then
                log_warn "日志文件 $(basename $log_file) 最近有 $recent_errors 个错误"
            else
                log_info "日志文件 $(basename $log_file) 无最近错误"
            fi
        fi
    done
}

# 检查网络连通性
check_network_connectivity() {
    log_info "检查网络连通性..."
    
    # 检查DNS解析
    if nslookup matrix.org > /dev/null 2>&1; then
        log_info "DNS解析正常"
    else
        log_error "DNS解析失败"
    fi
    
    # 检查外网连通性
    if ping -c 1 8.8.8.8 > /dev/null 2>&1; then
        log_info "外网连通性正常"
    else
        log_error "外网连通性异常"
    fi
    
    # 检查Matrix联邦连通性
    if curl -s --max-time 10 "https://matrix.org/_matrix/federation/v1/version" > /dev/null; then
        log_info "Matrix联邦连通性正常"
    else
        log_warn "Matrix联邦连通性异常"
    fi
}

# 生成健康报告
generate_health_report() {
    log_info "生成健康检查报告..."
    
    local report_file="$INSTALL_PATH/logs/health-report-$(date +%Y%m%d_%H%M%S).json"
    
    cat > "$report_file" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "overall_status": "$OVERALL_STATUS",
    "issues_count": ${#ISSUES_FOUND[@]},
    "warnings_count": ${#WARNINGS_FOUND[@]},
    "issues": $(printf '%s\n' "${ISSUES_FOUND[@]}" | jq -R . | jq -s .),
    "warnings": $(printf '%s\n' "${WARNINGS_FOUND[@]}" | jq -R . | jq -s .),
    "system_info": {
        "uptime": "$(uptime -p)",
        "load_average": "$(uptime | awk -F'load average:' '{print $2}')",
        "memory_usage": "$(free -h | grep Mem | awk '{print $3"/"$2}')",
        "disk_usage": "$(df -h $INSTALL_PATH | awk 'NR==2 {print $3"/"$2" ("$5")"}')"
    }
}
EOF
    
    log_info "健康报告已保存: $report_file"
}

# 发送通知
send_notification() {
    if [[ "$NOTIFICATION_ENABLED" != "true" ]]; then
        return 0
    fi
    
    local status="$OVERALL_STATUS"
    local message="Matrix健康检查完成 - 状态: $status"
    
    if [[ ${#ISSUES_FOUND[@]} -gt 0 ]]; then
        message="$message\n发现 ${#ISSUES_FOUND[@]} 个问题:\n$(printf '- %s\n' "${ISSUES_FOUND[@]}")"
    fi
    
    if [[ ${#WARNINGS_FOUND[@]} -gt 0 ]]; then
        message="$message\n发现 ${#WARNINGS_FOUND[@]} 个警告:\n$(printf '- %s\n' "${WARNINGS_FOUND[@]}")"
    fi
    
    # Webhook通知
    if [[ -n "$WEBHOOK_URL" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\": \"$message\"}" \
            > /dev/null 2>&1 || log_warn "Webhook通知发送失败"
    fi
    
    # 邮件通知
    if [[ -n "$EMAIL_TO" ]] && command -v mail &> /dev/null; then
        echo -e "$message" | mail -s "Matrix健康检查报告 - $status" "$EMAIL_TO" \
            > /dev/null 2>&1 || log_warn "邮件通知发送失败"
    fi
}

# 主函数
main() {
    log_info "开始Matrix健康检查..."
    
    # 创建日志目录
    mkdir -p "$(dirname $LOG_FILE)"
    
    # 执行各项检查
    check_docker_services
    check_port_connectivity
    check_ssl_certificates
    check_disk_space
    check_memory_usage
    check_system_load
    check_database_status
    check_log_errors
    check_network_connectivity
    
    # 生成报告和通知
    generate_health_report
    send_notification
    
    # 输出总结
    echo ""
    log_info "健康检查完成 - 总体状态: $OVERALL_STATUS"
    
    if [[ ${#ISSUES_FOUND[@]} -gt 0 ]]; then
        log_error "发现 ${#ISSUES_FOUND[@]} 个问题需要处理"
        exit 1
    elif [[ ${#WARNINGS_FOUND[@]} -gt 0 ]]; then
        log_warn "发现 ${#WARNINGS_FOUND[@]} 个警告需要关注"
        exit 2
    else
        log_info "所有检查项目正常"
        exit 0
    fi
}

# 显示帮助
show_help() {
    cat << EOF
Matrix Synapse 健康检查脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -p, --path PATH         指定安装路径 (默认: /opt/matrix)
    -c, --config FILE       指定配置文件
    --disk-threshold NUM    磁盘使用率阈值 (默认: 80)
    --memory-threshold NUM  内存使用率阈值 (默认: 85)
    --cert-days NUM         证书过期提醒天数 (默认: 30)
    --enable-notification   启用通知
    --webhook-url URL       Webhook通知URL
    --email-to EMAIL        邮件通知地址

退出代码:
    0: 所有检查正常
    1: 发现严重问题
    2: 发现警告

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--path)
            INSTALL_PATH="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --disk-threshold)
            DISK_USAGE_THRESHOLD="$2"
            shift 2
            ;;
        --memory-threshold)
            MEMORY_USAGE_THRESHOLD="$2"
            shift 2
            ;;
        --cert-days)
            CERT_EXPIRY_DAYS="$2"
            shift 2
            ;;
        --enable-notification)
            NOTIFICATION_ENABLED="true"
            shift
            ;;
        --webhook-url)
            WEBHOOK_URL="$2"
            shift 2
            ;;
        --email-to)
            EMAIL_TO="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
