#!/bin/bash

# Matrix Synapse 证书管理脚本
# 使用acme.sh和Cloudflare DNS API自动管理SSL证书
# 支持证书申请、续期、部署和监控

# 版本: 1.0.0
# 日期: 2025-07-13

set -e

# 配置变量
INSTALL_PATH="${INSTALL_PATH:-/opt/matrix}"
DOMAIN_NAME="${DOMAIN_NAME:-}"
CF_TOKEN="${CF_TOKEN:-}"
CERT_EMAIL="${CERT_EMAIL:-}"
LOG_FILE="$INSTALL_PATH/logs/certificate.log"

# acme.sh配置
ACME_HOME="${ACME_HOME:-/root/.acme.sh}"
CERT_PATH="$INSTALL_PATH/certs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warn() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查acme.sh
    if [[ ! -f "$ACME_HOME/acme.sh" ]]; then
        log_error "acme.sh未安装，请先运行部署脚本"
        exit 1
    fi
    
    # 检查必要参数
    if [[ -z "$DOMAIN_NAME" ]]; then
        log_error "未设置域名，请设置DOMAIN_NAME环境变量"
        exit 1
    fi
    
    if [[ -z "$CF_TOKEN" ]]; then
        log_error "未设置Cloudflare API Token，请设置CF_TOKEN环境变量"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl未安装"
        exit 1
    fi
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    export CF_Token="$CF_TOKEN"
    export CERT_HOME="$CERT_PATH"
    
    # 创建证书目录
    mkdir -p "$CERT_PATH"
    
    log_info "环境变量设置完成"
}

# 申请新证书
issue_certificate() {
    log_info "申请SSL证书..."
    
    local domain_args="-d $DOMAIN_NAME"
    
    # 如果需要通配符证书
    if [[ "$1" == "--wildcard" ]]; then
        domain_args="-d $DOMAIN_NAME -d *.$DOMAIN_NAME"
        log_info "申请通配符证书"
    fi
    
    # 使用acme.sh申请证书
    if "$ACME_HOME/acme.sh" --issue \
        --dns dns_cf \
        $domain_args \
        --keylength ec-256 \
        --force; then
        log_info "证书申请成功"
        return 0
    else
        log_error "证书申请失败"
        return 1
    fi
}

# 安装证书
install_certificate() {
    log_info "安装SSL证书..."
    
    # 安装证书到指定目录
    if "$ACME_HOME/acme.sh" --install-cert \
        -d "$DOMAIN_NAME" \
        --cert-file "$CERT_PATH/cert.pem" \
        --key-file "$CERT_PATH/key.pem" \
        --fullchain-file "$CERT_PATH/fullchain.pem" \
        --ca-file "$CERT_PATH/ca.pem" \
        --reloadcmd "systemctl reload nginx || docker-compose -f $INSTALL_PATH/docker-compose.yml restart nginx"; then
        
        log_info "证书安装成功"
        
        # 设置证书文件权限
        chmod 644 "$CERT_PATH"/*.pem
        chown root:root "$CERT_PATH"/*.pem
        
        return 0
    else
        log_error "证书安装失败"
        return 1
    fi
}

# 续期证书
renew_certificate() {
    log_info "续期SSL证书..."
    
    if "$ACME_HOME/acme.sh" --renew -d "$DOMAIN_NAME" --force; then
        log_info "证书续期成功"
        install_certificate
        return 0
    else
        log_error "证书续期失败"
        return 1
    fi
}

# 检查证书状态
check_certificate_status() {
    log_info "检查证书状态..."
    
    local cert_file="$CERT_PATH/fullchain.pem"
    
    if [[ ! -f "$cert_file" ]]; then
        log_warn "证书文件不存在: $cert_file"
        return 1
    fi
    
    # 检查证书有效期
    local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
    local expiry_timestamp=$(date -d "$expiry_date" +%s)
    local current_timestamp=$(date +%s)
    local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    log_info "证书过期时间: $expiry_date"
    log_info "距离过期还有: $days_until_expiry 天"
    
    # 检查证书主体
    local cert_subject=$(openssl x509 -in "$cert_file" -noout -subject | sed 's/.*CN=\([^,]*\).*/\1/')
    log_info "证书主体: $cert_subject"
    
    # 检查证书SAN
    local san_domains=$(openssl x509 -in "$cert_file" -noout -text | grep -A1 "Subject Alternative Name" | tail -1 | sed 's/.*DNS://g' | sed 's/, DNS:/, /g')
    log_info "SAN域名: $san_domains"
    
    # 返回剩余天数
    echo "$days_until_expiry"
}

# 自动续期检查
auto_renew_check() {
    log_info "执行自动续期检查..."
    
    local days_left=$(check_certificate_status)
    local renew_threshold=30
    
    if [[ $days_left -lt $renew_threshold ]]; then
        log_info "证书将在 $days_left 天后过期，开始自动续期..."
        
        if renew_certificate; then
            log_info "自动续期成功"
            
            # 发送通知
            send_notification "SUCCESS" "SSL证书自动续期成功，新证书有效期90天"
        else
            log_error "自动续期失败"
            send_notification "ERROR" "SSL证书自动续期失败，请手动处理"
            return 1
        fi
    else
        log_info "证书还有 $days_left 天过期，无需续期"
    fi
}

# 验证证书
verify_certificate() {
    log_info "验证SSL证书..."
    
    local cert_file="$CERT_PATH/fullchain.pem"
    local key_file="$CERT_PATH/key.pem"
    
    if [[ ! -f "$cert_file" || ! -f "$key_file" ]]; then
        log_error "证书或密钥文件不存在"
        return 1
    fi
    
    # 验证证书和密钥匹配
    local cert_hash=$(openssl x509 -noout -modulus -in "$cert_file" | openssl md5)
    local key_hash=$(openssl rsa -noout -modulus -in "$key_file" | openssl md5)
    
    if [[ "$cert_hash" == "$key_hash" ]]; then
        log_info "证书和密钥匹配验证成功"
    else
        log_error "证书和密钥不匹配"
        return 1
    fi
    
    # 验证证书链
    if openssl verify -CAfile "$CERT_PATH/ca.pem" "$cert_file" > /dev/null 2>&1; then
        log_info "证书链验证成功"
    else
        log_warn "证书链验证失败，但可能仍然有效"
    fi
    
    # 测试HTTPS连接
    if curl -s --max-time 10 "https://$DOMAIN_NAME:8443/_matrix/client/versions" > /dev/null; then
        log_info "HTTPS连接测试成功"
    else
        log_warn "HTTPS连接测试失败"
    fi
}

# 备份证书
backup_certificate() {
    log_info "备份SSL证书..."
    
    local backup_dir="$INSTALL_PATH/backups/certificates/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份证书文件
    if [[ -d "$CERT_PATH" ]]; then
        cp -r "$CERT_PATH"/* "$backup_dir/"
        log_info "证书备份完成: $backup_dir"
    else
        log_warn "证书目录不存在，跳过备份"
    fi
    
    # 备份acme.sh配置
    if [[ -d "$ACME_HOME" ]]; then
        tar -czf "$backup_dir/acme.sh-config.tar.gz" -C "$ACME_HOME" .
        log_info "acme.sh配置备份完成"
    fi
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"
    
    # 写入系统日志
    logger -t "matrix-cert" "$status: $message"
    
    # 如果配置了Webhook，发送通知
    if [[ -n "$WEBHOOK_URL" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\": \"Matrix证书管理: $message\"}" \
            > /dev/null 2>&1 || log_warn "Webhook通知发送失败"
    fi
}

# 显示证书信息
show_certificate_info() {
    log_info "显示证书信息..."
    
    local cert_file="$CERT_PATH/fullchain.pem"
    
    if [[ ! -f "$cert_file" ]]; then
        log_error "证书文件不存在"
        return 1
    fi
    
    echo ""
    echo "=== SSL证书信息 ==="
    openssl x509 -in "$cert_file" -noout -text | grep -A2 "Subject:"
    openssl x509 -in "$cert_file" -noout -text | grep -A1 "Not Before"
    openssl x509 -in "$cert_file" -noout -text | grep -A1 "Not After"
    openssl x509 -in "$cert_file" -noout -text | grep -A3 "Subject Alternative Name"
    echo "==================="
    echo ""
}

# 主函数
main() {
    local action="$1"
    
    # 创建日志目录
    mkdir -p "$(dirname $LOG_FILE)"
    
    log_info "开始证书管理操作: $action"
    
    case "$action" in
        "issue")
            check_dependencies
            setup_environment
            backup_certificate
            issue_certificate "$2"
            install_certificate
            verify_certificate
            ;;
        "renew")
            check_dependencies
            setup_environment
            backup_certificate
            renew_certificate
            verify_certificate
            ;;
        "auto-renew")
            check_dependencies
            setup_environment
            auto_renew_check
            ;;
        "status")
            check_certificate_status
            show_certificate_info
            ;;
        "verify")
            verify_certificate
            ;;
        "backup")
            backup_certificate
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 显示帮助
show_help() {
    cat << EOF
Matrix Synapse 证书管理脚本

用法: $0 <操作> [选项]

操作:
    issue [--wildcard]      申请新证书 (可选通配符)
    renew                   手动续期证书
    auto-renew              自动续期检查
    status                  显示证书状态
    verify                  验证证书
    backup                  备份证书

环境变量:
    DOMAIN_NAME             域名
    CF_TOKEN                Cloudflare API Token
    CERT_EMAIL              证书邮箱
    INSTALL_PATH            安装路径 (默认: /opt/matrix)

示例:
    $0 issue                # 申请标准证书
    $0 issue --wildcard     # 申请通配符证书
    $0 renew                # 手动续期
    $0 auto-renew           # 自动续期检查
    $0 status               # 查看证书状态

注意:
    - 首次使用前请确保已设置正确的环境变量
    - 建议将auto-renew添加到crontab中定期执行
    - 证书申请需要域名DNS解析到Cloudflare

EOF
}

# 检查参数
if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@"
