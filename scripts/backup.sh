#!/bin/bash

# Matrix Synapse 自动备份脚本
# 备份SQLite数据库、配置文件、证书和日志
# 支持本地备份和远程备份
# 包含备份验证和清理功能

# 版本: 1.0.0
# 日期: 2025-07-13

set -e

# 配置变量
INSTALL_PATH="${INSTALL_PATH:-/opt/matrix}"
BACKUP_PATH="${BACKUP_PATH:-$INSTALL_PATH/backups}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
LOG_FILE="$INSTALL_PATH/logs/backup.log"

# 远程备份配置 (可选)
REMOTE_BACKUP_ENABLED="${REMOTE_BACKUP_ENABLED:-false}"
REMOTE_BACKUP_HOST="${REMOTE_BACKUP_HOST:-}"
REMOTE_BACKUP_USER="${REMOTE_BACKUP_USER:-}"
REMOTE_BACKUP_PATH="${REMOTE_BACKUP_PATH:-}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warn() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    local deps=("tar" "gzip" "sqlite3")
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "缺少依赖: $dep"
            exit 1
        fi
    done
    
    if [[ "$REMOTE_BACKUP_ENABLED" == "true" ]]; then
        if ! command -v "rsync" &> /dev/null; then
            log_error "远程备份需要rsync"
            exit 1
        fi
    fi
}

# 创建备份目录
create_backup_dirs() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    BACKUP_DIR="$BACKUP_PATH/$timestamp"
    
    mkdir -p "$BACKUP_DIR"/{database,configs,certs,logs}
    log_info "创建备份目录: $BACKUP_DIR"
}

# 备份SQLite数据库
backup_database() {
    log_info "开始备份数据库..."
    
    local db_path="$INSTALL_PATH/data/homeserver.db"
    local backup_db="$BACKUP_DIR/database/homeserver.db"
    
    if [[ ! -f "$db_path" ]]; then
        log_warn "数据库文件不存在: $db_path"
        return 1
    fi
    
    # 使用SQLite的备份命令确保一致性
    if sqlite3 "$db_path" ".backup '$backup_db'"; then
        log_info "数据库备份完成: $backup_db"
        
        # 验证备份
        if sqlite3 "$backup_db" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_info "数据库备份验证成功"
        else
            log_error "数据库备份验证失败"
            return 1
        fi
    else
        log_error "数据库备份失败"
        return 1
    fi
    
    # 备份WAL和SHM文件 (如果存在)
    for ext in "wal" "shm"; do
        local wal_file="$db_path-$ext"
        if [[ -f "$wal_file" ]]; then
            cp "$wal_file" "$BACKUP_DIR/database/"
            log_info "备份WAL文件: $wal_file"
        fi
    done
}

# 备份配置文件
backup_configs() {
    log_info "开始备份配置文件..."
    
    local config_source="$INSTALL_PATH/configs"
    local config_backup="$BACKUP_DIR/configs"
    
    if [[ -d "$config_source" ]]; then
        cp -r "$config_source"/* "$config_backup/"
        log_info "配置文件备份完成"
    else
        log_warn "配置目录不存在: $config_source"
    fi
    
    # 备份docker-compose.yml
    if [[ -f "$INSTALL_PATH/docker-compose.yml" ]]; then
        cp "$INSTALL_PATH/docker-compose.yml" "$config_backup/"
        log_info "Docker Compose配置备份完成"
    fi
}

# 备份SSL证书
backup_certificates() {
    log_info "开始备份SSL证书..."
    
    local cert_source="$INSTALL_PATH/certs"
    local cert_backup="$BACKUP_DIR/certs"
    
    if [[ -d "$cert_source" ]]; then
        cp -r "$cert_source"/* "$cert_backup/"
        log_info "SSL证书备份完成"
    else
        log_warn "证书目录不存在: $cert_source"
    fi
    
    # 备份acme.sh证书
    if [[ -d "/root/.acme.sh" ]]; then
        mkdir -p "$cert_backup/acme.sh"
        cp -r /root/.acme.sh/* "$cert_backup/acme.sh/"
        log_info "acme.sh证书备份完成"
    fi
}

# 备份重要日志
backup_logs() {
    log_info "开始备份日志文件..."
    
    local log_source="$INSTALL_PATH/logs"
    local log_backup="$BACKUP_DIR/logs"
    
    if [[ -d "$log_source" ]]; then
        # 只备份最近7天的日志
        find "$log_source" -name "*.log" -mtime -7 -exec cp {} "$log_backup/" \;
        log_info "日志文件备份完成"
    else
        log_warn "日志目录不存在: $log_source"
    fi
}

# 创建备份元数据
create_metadata() {
    log_info "创建备份元数据..."
    
    local metadata_file="$BACKUP_DIR/backup_info.json"
    
    cat > "$metadata_file" << EOF
{
    "backup_time": "$(date -Iseconds)",
    "backup_type": "full",
    "matrix_version": "$(docker exec matrix-synapse cat /usr/local/lib/python*/site-packages/synapse/__init__.py | grep __version__ | cut -d'"' -f2 2>/dev/null || echo 'unknown')",
    "backup_size": "$(du -sh $BACKUP_DIR | cut -f1)",
    "files_count": $(find "$BACKUP_DIR" -type f | wc -l),
    "hostname": "$(hostname)",
    "install_path": "$INSTALL_PATH"
}
EOF
    
    log_info "备份元数据创建完成"
}

# 压缩备份
compress_backup() {
    log_info "开始压缩备份..."
    
    local archive_name="matrix_backup_$(basename $BACKUP_DIR).tar.gz"
    local archive_path="$BACKUP_PATH/$archive_name"
    
    cd "$BACKUP_PATH"
    if tar -czf "$archive_name" "$(basename $BACKUP_DIR)"; then
        log_info "备份压缩完成: $archive_path"
        
        # 删除未压缩的目录
        rm -rf "$BACKUP_DIR"
        
        # 记录压缩后大小
        local size=$(du -sh "$archive_path" | cut -f1)
        log_info "压缩后大小: $size"
        
        BACKUP_ARCHIVE="$archive_path"
    else
        log_error "备份压缩失败"
        return 1
    fi
}

# 远程备份
remote_backup() {
    if [[ "$REMOTE_BACKUP_ENABLED" != "true" ]]; then
        return 0
    fi
    
    log_info "开始远程备份..."
    
    if [[ -z "$REMOTE_BACKUP_HOST" || -z "$REMOTE_BACKUP_USER" || -z "$REMOTE_BACKUP_PATH" ]]; then
        log_error "远程备份配置不完整"
        return 1
    fi
    
    local remote_dest="$REMOTE_BACKUP_USER@$REMOTE_BACKUP_HOST:$REMOTE_BACKUP_PATH/"
    
    if rsync -avz --progress "$BACKUP_ARCHIVE" "$remote_dest"; then
        log_info "远程备份完成"
    else
        log_error "远程备份失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "开始清理旧备份..."
    
    local deleted_count=0
    
    # 清理本地备份
    while IFS= read -r -d '' file; do
        rm -f "$file"
        ((deleted_count++))
        log_info "删除旧备份: $(basename $file)"
    done < <(find "$BACKUP_PATH" -name "matrix_backup_*.tar.gz" -mtime +$BACKUP_RETENTION_DAYS -print0)
    
    if [[ $deleted_count -eq 0 ]]; then
        log_info "没有需要清理的旧备份"
    else
        log_info "清理了 $deleted_count 个旧备份文件"
    fi
    
    # 清理远程备份 (如果启用)
    if [[ "$REMOTE_BACKUP_ENABLED" == "true" ]]; then
        log_info "清理远程旧备份..."
        ssh "$REMOTE_BACKUP_USER@$REMOTE_BACKUP_HOST" \
            "find $REMOTE_BACKUP_PATH -name 'matrix_backup_*.tar.gz' -mtime +$BACKUP_RETENTION_DAYS -delete"
    fi
}

# 验证备份
verify_backup() {
    log_info "验证备份完整性..."
    
    if [[ ! -f "$BACKUP_ARCHIVE" ]]; then
        log_error "备份文件不存在: $BACKUP_ARCHIVE"
        return 1
    fi
    
    # 测试压缩文件完整性
    if tar -tzf "$BACKUP_ARCHIVE" > /dev/null; then
        log_info "备份文件完整性验证成功"
    else
        log_error "备份文件损坏"
        return 1
    fi
    
    # 检查备份大小
    local size=$(stat -c%s "$BACKUP_ARCHIVE")
    if [[ $size -lt 1024 ]]; then
        log_error "备份文件过小，可能不完整"
        return 1
    fi
    
    log_info "备份验证完成"
}

# 发送通知 (可选)
send_notification() {
    local status="$1"
    local message="$2"
    
    # 这里可以添加邮件、Webhook等通知方式
    # 示例: 写入系统日志
    logger -t "matrix-backup" "$status: $message"
}

# 主函数
main() {
    log_info "开始Matrix备份任务..."
    
    # 创建日志目录
    mkdir -p "$(dirname $LOG_FILE)"
    
    # 检查依赖
    check_dependencies
    
    # 创建备份目录
    create_backup_dirs
    
    # 执行备份
    local backup_success=true
    
    backup_database || backup_success=false
    backup_configs || backup_success=false
    backup_certificates || backup_success=false
    backup_logs || backup_success=false
    
    if [[ "$backup_success" == "true" ]]; then
        create_metadata
        compress_backup
        verify_backup
        remote_backup
        cleanup_old_backups
        
        log_info "备份任务完成成功"
        send_notification "SUCCESS" "Matrix备份完成: $BACKUP_ARCHIVE"
    else
        log_error "备份任务失败"
        send_notification "FAILED" "Matrix备份失败，请检查日志"
        exit 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
Matrix Synapse 备份脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -p, --path PATH         指定安装路径 (默认: /opt/matrix)
    -b, --backup-path PATH  指定备份路径 (默认: /opt/matrix/backups)
    -r, --retention DAYS    备份保留天数 (默认: 30)
    --remote-enable         启用远程备份
    --remote-host HOST      远程主机
    --remote-user USER      远程用户
    --remote-path PATH      远程路径

环境变量:
    INSTALL_PATH            安装路径
    BACKUP_PATH             备份路径
    BACKUP_RETENTION_DAYS   保留天数
    REMOTE_BACKUP_ENABLED   启用远程备份
    REMOTE_BACKUP_HOST      远程主机
    REMOTE_BACKUP_USER      远程用户
    REMOTE_BACKUP_PATH      远程路径

示例:
    $0                      # 使用默认配置备份
    $0 -p /opt/matrix -r 7  # 指定路径和保留7天
    $0 --remote-enable --remote-host backup.example.com --remote-user backup --remote-path /backups/matrix

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--path)
            INSTALL_PATH="$2"
            shift 2
            ;;
        -b|--backup-path)
            BACKUP_PATH="$2"
            shift 2
            ;;
        -r|--retention)
            BACKUP_RETENTION_DAYS="$2"
            shift 2
            ;;
        --remote-enable)
            REMOTE_BACKUP_ENABLED="true"
            shift
            ;;
        --remote-host)
            REMOTE_BACKUP_HOST="$2"
            shift 2
            ;;
        --remote-user)
            REMOTE_BACKUP_USER="$2"
            shift 2
            ;;
        --remote-path)
            REMOTE_BACKUP_PATH="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
