# Matrix Synapse 自动部署项目需求总结

## 项目概述

Matrix Synapse自动部署项目是一个企业级的Matrix服务器自动化部署解决方案，支持完整的SSL证书管理、Docker容器化部署、RouterOS网络集成和智能监控系统。

## 用户偏好和配置要求

基于用户的具体需求和偏好，本项目针对以下特定场景进行了优化：

### 语言偏好
- **中文支持**: 用户偏好中文交流，所有日志、文档和界面信息优先使用中文
- **双语文档**: 关键技术文档提供中英文对照

### 部署环境偏好
- **SQLite数据库**: 用户偏好SQLite作为Matrix部署的默认数据库，简化部署和维护
- **Docker Compose V2**: 严格遵循Docker Compose V2标准，使用现代语法 `docker compose`（空格）
- **内网环境**: 针对内网环境优化，支持RouterOS Legacy API集成

### 网络和域名配置
- **自定义子域名**: 支持用户自定义子域名前缀（默认：matrix/turn）
- **独立证书管理**: 每个子域名使用独立的SSL证书
- **ISP端口限制**: 特别考虑ISP阻塞80/443端口的情况，提供nginx配置解决方案
- **TURN域名优化**: 用户偏好完全移除TURN域名的nginx web服务配置，仅保留SSL证书

### 安全和可选功能
- **安全功能可选**: 防火墙(ufw)和fail2ban等安全功能设为可选，通过用户提示确认
- **备份监控**: 要求具备完整的备份和监控功能
- **证书复用机制**: 强调避免浪费Let's Encrypt配额，优先复用现有证书

### 配置管理偏好
- **sed变量替换**: 偏好使用sed进行变量替换，而非Mustache模板
- **系统化验证**: 要求每个优化步骤后进行系统化验证
- **无警告配置**: Docker Compose配置必须无警告，环境变量命名保持一致

### 目录结构偏好
- **内部目录分离**: 偏好将用户文档与内部部署文件分离
- **internal目录**: 创建'internal/'目录结构存放核心部署文件
- **根目录文档**: 文档和示例保留在根目录，保持用户友好
- **证书软链接**: 偏好证书软链接机制，避免重复文件和实时同步

## 1. 项目需求总结

### 1.1 核心功能需求

#### 自动化部署系统
- **一键部署**: 通过单个脚本完成Matrix Synapse的完整部署
- **配置模板化**: 支持可定制的配置模板，适应不同环境需求
- **环境检测**: 自动检测系统环境并进行必要的依赖安装
- **错误恢复**: 具备完善的错误处理和恢复机制

#### 智能证书管理系统
- **Let's Encrypt集成**: 自动申请和管理Let's Encrypt SSL证书
- **配额保护机制**: 智能检测现有证书，避免重复申请浪费配额
- **软链接机制**: 实现证书文件的软链接管理，提高效率
- **多域名支持**: 支持基础域名、Matrix域名、TURN域名的独立证书管理
- **自动续期**: 定时检查和自动续期即将过期的证书

#### Docker容器化部署
- **镜像优化**: 使用官方推荐的Docker镜像，支持镜像源切换
- **网络问题解决**: 提供Docker镜像拉取失败的完整解决方案
- **容器编排**: 使用Docker Compose管理多服务容器
- **数据持久化**: 正确配置数据卷和持久化存储

#### RouterOS网络集成
- **动态IP监控**: 实时监控和更新动态IP地址
- **防火墙规则管理**: 自动配置RouterOS防火墙规则
- **端口转发**: 自动设置必要的端口转发规则
- **网络故障恢复**: 网络变化时的自动恢复机制

### 1.2 技术栈和依赖关系

#### 核心技术栈
- **操作系统**: Linux (Ubuntu/Debian/CentOS)
- **容器化**: Docker & Docker Compose V2
- **Web服务器**: Nginx (反向代理)
- **SSL证书**: acme.sh + Let's Encrypt
- **数据库**: SQLite (默认) / PostgreSQL (可选)
- **编程语言**: Bash Shell, Python 3.x

#### 主要依赖（基于2025年7月最新版本）
- **系统依赖**: curl, openssl, git, cron
- **Docker**: Docker Engine 24.0+, Docker Compose V2 (严格使用 `docker compose` 语法)
- **Python**: Python 3.9+ (Debian 12特别注意事项见下文)
- **网络**: Cloudflare DNS API (证书申请)
- **RouterOS**: RouterOS 7.x API (基于最新官方文档)
- **数据库**: SQLite 3.35+ (用户偏好的默认选择)

#### 技术栈版本要求（2025年7月最新）
- **Matrix Synapse**: v1.110+ (基于官方最新稳定版)
- **Coturn**: v4.6+ (最新稳定版本)
- **Nginx**: 1.24+ (官方推荐版本)
- **acme.sh**: v3.0.7+ (Let's Encrypt最新API支持)
- **Docker镜像**:
  - `ghcr.io/element-hq/synapse:latest` (官方推荐)
  - `coturn/coturn:latest` (官方维护)
  - `nginx:alpine` (轻量级官方镜像)

#### 用户特定配置
- **语言环境**: 中文优先的日志和界面
- **域名配置**: 支持自定义子域名前缀 (默认: matrix/turn)
- **证书策略**: 独立子域名证书 + 软链接机制
- **安全选项**: 可选的防火墙和fail2ban配置
- **网络适配**: ISP端口阻塞的特殊处理

## 官方最新资料要求（2025年7月）

### 技术组件版本管理

本项目严格基于2025年7月的官方最新资料和文档，确保所有技术组件都使用最新稳定版本：

#### 版本检查和更新机制
```bash
# 检查Matrix Synapse最新版本
curl -s https://api.github.com/repos/element-hq/synapse/releases/latest | grep tag_name

# 检查Docker镜像最新版本
docker pull ghcr.io/element-hq/synapse:latest
docker pull coturn/coturn:latest
docker pull nginx:alpine

# 检查acme.sh最新版本
curl -s https://api.github.com/repos/acmesh-official/acme.sh/releases/latest | grep tag_name

# 检查RouterOS最新API文档
# 访问: https://help.mikrotik.com/docs/display/ROS/REST+API
```

#### 官方文档参考源
- **Matrix Synapse**: https://matrix-org.github.io/synapse/latest/
- **Coturn**: https://github.com/coturn/coturn/wiki
- **RouterOS API**: https://help.mikrotik.com/docs/display/ROS/REST+API
- **Let's Encrypt**: https://letsencrypt.org/docs/
- **acme.sh**: https://github.com/acmesh-official/acme.sh/wiki
- **Docker Compose**: https://docs.docker.com/compose/compose-file/

#### 版本兼容性验证
- 定期检查官方发布说明和变更日志
- 验证新版本与现有配置的兼容性
- 测试新版本的功能和性能表现
- 更新配置模板以适应新版本要求

## 2. 已解决的问题清单

### 2.1 证书管理问题

#### 问题1: 证书路径验证问题
- **问题描述**: 智能证书管理脚本的路径配置硬编码，在不同用户环境下无法正确工作
- **根本原因**: `ACME_HOME` 硬编码为 `/root/.acme.sh`，缺少路径自动检测机制
- **解决方案**: 实现智能路径检测，支持多个常见安装位置的自动发现
- **验证结果**: ✅ 支持用户环境和root环境的自动适配

#### 问题2: Let's Encrypt配额浪费
- **问题描述**: 每次部署都重新申请证书，浪费Let's Encrypt配额
- **根本原因**: 缺少现有证书的检测和复用机制
- **解决方案**: 实现智能证书检测和软链接机制，优先复用现有证书
- **验证结果**: ✅ 有效保护Let's Encrypt配额，避免不必要的重复申请

#### 问题3: 证书状态检查逻辑错误
- **问题描述**: 现有证书有效期到2025-09-10，但脚本没有正确识别
- **根本原因**: 日期解析在不同操作系统上存在兼容性问题，缺少acme.sh状态检查
- **解决方案**: 增强日期解析兼容性，添加acme.sh证书状态检查功能
- **验证结果**: ✅ 正确识别有效证书，支持macOS和Linux环境

### 2.2 部署脚本问题

#### 问题4: Synapse签名密钥生成失败
- **问题描述**: 错误信息 "Environment variable 'SYNAPSE_SERVER_NAME' is mandatory in generate mode"
- **根本原因**: Docker命令缺少必需的环境变量，使用了错误的变量名
- **解决方案**: 修复环境变量传递，正确设置 `SYNAPSE_SERVER_NAME` 和 `SYNAPSE_REPORT_STATS`
- **验证结果**: ✅ Synapse签名密钥生成成功

#### 问题5: 部署脚本提前退出
- **问题描述**: 证书管理完成后脚本停止执行，没有继续后续步骤
- **根本原因**: 主函数没有正确处理返回值，变量初始化问题
- **解决方案**: 修复返回值处理逻辑，确保脚本完整执行
- **验证结果**: ✅ 部署脚本完整执行所有步骤

### 2.3 Docker镜像问题

#### 问题6: Docker镜像源问题
- **问题描述**: 使用过时的镜像源，网络连接超时导致部署失败
- **根本原因**: 镜像源配置过时，缺少网络问题的解决方案
- **解决方案**: 更新为官方推荐镜像源，提供完整的网络问题解决方案
- **验证结果**: ✅ 支持多种网络环境，提供镜像加速器配置

### 2.4 环境兼容性问题

#### 问题7: 跨平台兼容性
- **问题描述**: macOS和Linux的date命令差异导致脚本失败
- **根本原因**: 硬编码使用Linux特定的date命令格式
- **解决方案**: 添加操作系统检测和兼容性处理
- **验证结果**: ✅ 支持Ubuntu和Debian环境

#### 问题8: Debian 12 Python环境问题
- **问题描述**: Debian 12的Python环境管理策略变化导致脚本执行失败
- **根本原因**: Debian 12引入`externally-managed-environment`限制，禁止系统级pip安装
- **解决方案**: 实现Python虚拟环境支持，提供多种安装方式
- **验证结果**: ✅ 支持Debian 12的Python虚拟环境和系统包管理

## 3. 实现的核心功能

### 3.1 智能证书管理系统

#### 软链接机制
- **证书复用**: 创建指向acme.sh证书的软链接，避免文件重复
- **实时同步**: 证书更新时自动同步到服务
- **存储优化**: 减少磁盘空间占用

#### 配额保护机制
- **智能检测**: 自动检查现有证书的有效期
- **决策逻辑**: 仅在证书不存在或即将过期时申请新证书
- **状态管理**: 区分成功、跳过、失败三种状态

#### 多域名管理
- **独立证书**: 支持基础域名、Matrix域名、TURN域名的独立管理
- **批量操作**: 支持批量证书申请和管理
- **状态监控**: 详细的证书状态报告和监控

### 3.2 Docker镜像优化

#### 官方镜像支持
- **Synapse**: 使用 `ghcr.io/element-hq/synapse:latest` (官方推荐)
- **备用镜像**: 保留 `matrixdotorg/synapse:latest` 作为备用
- **其他服务**: Coturn、Nginx等使用官方推荐镜像

#### 网络问题解决
- **镜像加速器**: 支持多个国内镜像源
- **智能重试**: 自动重试和故障转移机制
- **离线支持**: 支持离线镜像包部署

### 3.3 系统优化和清理

#### Internal目录优化
- **文件清理**: 移除所有临时文件、缓存文件、测试文件
- **结构整理**: 保持清晰的目录结构，只包含核心部署文件
- **权限管理**: 确保所有脚本具有正确的可执行权限

#### 跨平台兼容
- **操作系统适配**: 支持不同Linux发行版和macOS
- **路径处理**: 智能处理不同环境下的路径差异
- **命令兼容**: 处理不同系统命令的差异

## 4. 注意事项和最佳实践

### 4.1 部署前准备

#### 系统要求（基于2025年7月最新支持）
- **操作系统**: Ubuntu 20.04+ / Debian 11+ (Debian 12特别注意事项见下文)
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低20GB可用空间
- **网络**: 稳定的互联网连接，考虑ISP端口80/443阻塞情况
- **Python环境**: Python 3.9+ (Debian 12需要特殊配置)

#### Debian 12 Python执行问题及解决方案
Debian 12引入了更严格的Python环境管理，可能导致Python脚本执行问题：

**问题现象**:
- Python脚本无法执行或提示权限错误
- 模块导入失败或依赖包缺失
- `externally-managed-environment` 错误

**解决方案**:
```bash
# 1. 安装Python虚拟环境支持
sudo apt update
sudo apt install python3-venv python3-pip

# 2. 创建虚拟环境（推荐方式）
python3 -m venv /opt/matrix/venv
source /opt/matrix/venv/bin/activate

# 3. 在虚拟环境中安装依赖
pip install -r internal/routeros/requirements.txt

# 4. 修改脚本执行方式
# 在脚本中使用虚拟环境的Python
#!/opt/matrix/venv/bin/python3

# 5. 系统级安装（不推荐，但可用）
sudo apt install python3-requests python3-routeros-api --break-system-packages
```

**最佳实践**:
- 优先使用虚拟环境隔离Python依赖
- 避免使用 `--break-system-packages` 选项
- 在部署脚本中自动创建和激活虚拟环境

#### 环境配置（基于用户偏好和2025年7月最新要求）
- **域名准备**: 准备基础域名、Matrix子域名、TURN子域名
  - 支持自定义子域名前缀（默认：matrix.domain.com, turn.domain.com）
- **DNS配置**: 将域名解析到Cloudflare
- **API密钥**: 获取Cloudflare API Token（基于最新API文档）
- **数据库选择**: 默认使用SQLite 3.35+（用户偏好）
- **Docker环境**: 确保Docker Compose V2可用，使用 `docker compose` 命令
- **RouterOS配置**: 如需内网集成，准备RouterOS 7.x API访问（基于最新官方文档）
- **Python环境**: 配置Python 3.9+虚拟环境（Debian 12必需）
- **安全功能**: 根据需要选择性启用防火墙和fail2ban（可选功能）

#### 版本验证检查
```bash
# 验证系统环境版本
python3 --version  # 应为 3.9+
docker --version   # 应为 24.0+
docker compose version  # 应为 V2
sqlite3 --version  # 应为 3.35+

# 验证网络连接
curl -I https://api.github.com/  # GitHub API连接
curl -I https://api.cloudflare.com/  # Cloudflare API连接
```

#### 特殊网络环境处理
- **ISP端口阻塞**: 如果ISP阻塞80/443端口，配置特殊的nginx规则
- **内网部署**: 支持RouterOS 7.x API的内网环境
- **证书管理**: 优先检查现有证书，避免浪费Let's Encrypt配额

### 4.2 证书管理最佳实践

#### 配额保护（用户重点关注）
- **智能模式**: 优先使用智能证书管理，避免不必要的申请
- **软链接机制**: 使用证书软链接，实现实时同步和避免重复申请
- **有效期检查**: 证书剩余有效期超过30天时不重新申请
- **状态检查**: 定期检查证书状态，确认有效期
- **备用方案**: 准备备用证书申请方案

#### 故障排除
- **路径验证**: 使用 `verify-paths` 命令检查证书路径配置
- **软链接检查**: 验证软链接目标文件的有效性
- **日志分析**: 查看详细的证书管理日志（中文输出）
- **手动恢复**: 必要时使用手动证书申请方式

#### TURN域名特殊处理（用户偏好）
- **仅SSL证书**: TURN域名仅申请SSL证书，不配置web服务
- **nginx配置**: 完全移除TURN域名的nginx web服务配置
- **证书独立**: TURN域名证书独立管理，支持软链接

#### Debian 12 Python环境故障排除

**常见错误信息**:
```
error: externally-managed-environment
This environment is externally managed
To install Python packages system-wide, try apt install python3-xyz
```

**详细解决步骤**:

1. **检查Python环境**:
```bash
python3 --version  # 确认Python版本
which python3      # 确认Python路径
pip3 --version     # 检查pip状态
```

2. **创建虚拟环境（推荐方案）**:
```bash
# 安装虚拟环境工具
sudo apt update
sudo apt install python3-venv python3-pip

# 创建项目虚拟环境
python3 -m venv /opt/matrix/venv

# 激活虚拟环境
source /opt/matrix/venv/bin/activate

# 安装依赖
pip install -r internal/routeros/requirements.txt
```

3. **系统包安装（备用方案）**:
```bash
# 安装系统Python包
sudo apt install python3-requests python3-routeros-api

# 如果包不存在，使用pip with override
pip install --break-system-packages routeros-api
```

4. **脚本修改**:
```bash
# 修改Python脚本shebang
#!/opt/matrix/venv/bin/python3

# 或在脚本中激活虚拟环境
source /opt/matrix/venv/bin/activate
python3 script.py
```

**自动化解决方案**:
部署脚本会自动检测Debian 12环境并创建虚拟环境。

### 4.3 系统维护建议

#### 定期维护（用户要求的备份监控功能）
- **证书监控**: 设置证书到期提醒，支持中文通知
- **系统更新**: 定期更新Docker镜像和系统包
- **备份管理**: 完整的备份功能，定期备份配置文件和数据
- **配置验证**: 每次优化后进行系统化验证（用户偏好）

#### 监控和告警
- **健康检查**: 使用内置的健康检查脚本
- **日志监控**: 监控系统和应用日志，中文日志输出
- **性能监控**: 监控系统资源使用情况
- **RouterOS集成**: 监控动态IP变化和网络状态

#### Docker管理（V2标准，基于2025年7月最新版本）
- **命令规范**: 严格使用 `docker compose`（空格）语法
- **配置标准**: 确保Docker Compose配置无警告
- **环境变量**: 保持一致的环境变量命名规范
- **镜像管理**: 支持镜像源切换和网络问题解决
- **版本要求**: Docker Engine 24.0+, Docker Compose V2

#### 技术栈版本管理和更新
```bash
# 定期检查和更新技术栈版本
./scripts/version-check.sh  # 检查所有组件版本
./scripts/update-check.sh   # 检查可用更新

# 手动版本检查
docker images | grep -E "(synapse|coturn|nginx)"  # 检查Docker镜像版本
python3 -c "import sqlite3; print(sqlite3.sqlite_version)"  # SQLite版本
```

#### Python环境管理（Debian 12特别支持）
- **虚拟环境**: 自动创建和管理Python虚拟环境
- **依赖管理**: 使用requirements.txt管理Python依赖
- **系统兼容**: 支持Debian 12的严格Python环境管理
- **故障恢复**: 提供多种Python安装方式的备用方案

### 4.4 安全考虑

#### 权限配置
- **最小权限**: 使用最小必要权限原则
- **文件权限**: 正确设置证书和配置文件权限
- **网络安全**: 可选的防火墙规则配置（用户提示确认）

#### 数据保护
- **敏感信息**: 保护Cloudflare API Token和RouterOS API密钥
- **备份加密**: 对备份数据进行加密
- **访问控制**: 限制对敏感文件的访问
- **证书安全**: 软链接机制确保证书文件安全访问

#### 用户偏好的安全配置
- **可选安全功能**: fail2ban和防火墙作为可选功能，通过用户提示确认
- **内网安全**: RouterOS Legacy API的安全配置
- **证书保护**: 避免证书重复申请，保护Let's Encrypt配额

## 5. 文件结构说明

### 5.1 Internal目录结构（用户偏好的分离设计）

```
internal/                               # 核心部署文件目录（用户偏好）
├── .gitignore                          # Git忽略规则
├── configs/                            # 配置模板目录
│   ├── coturn.conf.template           # Coturn配置模板
│   ├── homeserver.yaml.template       # Synapse配置模板（SQLite优化）
│   ├── log.config.template            # 日志配置模板（中文支持）
│   └── nginx.conf.template            # Nginx配置模板（ISP端口阻塞处理）
├── deploy.sh                          # 主部署脚本（中文日志）
├── docker-compose.yml                 # Docker V2服务配置（无警告）
├── install.sh                         # 快速安装脚本
├── logs/                              # 日志目录（运行时创建）
├── routeros/                          # RouterOS Legacy API集成
│   ├── ip-monitor.py                  # IP监控脚本
│   └── requirements.txt               # Python依赖
└── scripts/                           # 管理脚本目录
    ├── backup.sh                      # 备份脚本（用户要求功能）
    ├── certificate-manager.sh         # 智能证书管理脚本（软链接支持）
    ├── docker-image-fix.sh           # Docker镜像修复脚本
    └── health-check.sh               # 健康检查脚本


根目录/                                 # 用户文档和示例（用户偏好）
├── README.md                          # 项目说明文档
├── REQUIREMENTS.md                    # 需求总结文档
├── config-example.env                 # 环境变量配置示例
├── manual-guide.md                    # 手动部署指南
└── 其他文档...                        # 用户友好的文档
```

#### 目录设计理念（基于用户偏好）
- **分离原则**: 用户文档保留在根目录，核心部署文件放在internal/目录
- **整洁性**: internal目录只包含必要的部署文件，无临时文件
- **用户友好**: 根目录保持用户友好的文档和示例结构

### 5.2 核心脚本说明

#### 部署脚本
- **deploy.sh**: 主要部署脚本，执行完整的Matrix Synapse部署流程
- **install.sh**: 快速安装脚本，适用于标准环境的快速部署

#### 证书管理（用户重点关注的软链接机制）
- **certificate-manager.sh**: 智能证书管理脚本，支持软链接和配额保护
  - `smart`: 智能证书管理（推荐，支持软链接）
  - `status`: 检查证书状态（显示软链接信息）
  - `create-symlinks`: 创建证书软链接（用户偏好功能）
  - `verify-paths`: 验证证书路径配置
  - `force`: 强制重新申请（谨慎使用，保护配额）

#### 系统管理
- **backup.sh**: 系统备份脚本，备份配置和数据
- **health-check.sh**: 健康检查脚本，监控服务状态
- **docker-image-fix.sh**: Docker镜像问题修复脚本

#### 网络集成（基于RouterOS 7.x最新API）
- **ip-monitor.py**: RouterOS IP监控脚本，处理动态IP更新
- **API版本**: 支持RouterOS 7.x REST API（基于2025年7月最新文档）
- **Python环境**: 在Debian 12上使用虚拟环境运行

### 5.3 配置文件说明

#### 环境配置
- **config-example.env**: 环境变量配置示例（包含用户偏好设置）
- **config.env**: 实际环境变量配置（用户创建）

#### 服务配置模板（基于2025年7月最新版本优化）
- **homeserver.yaml.template**: Synapse v1.110+配置模板（SQLite优化，中文注释）
- **nginx.conf.template**: Nginx 1.24+反向代理配置模板（ISP端口阻塞处理）
- **coturn.conf.template**: Coturn v4.6+配置模板（无web服务配置）
- **log.config.template**: 日志配置模板（中文日志支持）

#### 用户偏好的配置特性（2025年7月标准）
- **sed变量替换**: 使用sed进行配置文件变量替换，而非Mustache模板
- **Docker V2标准**: 严格遵循Docker Compose V2语法规范
- **环境变量一致性**: 保持一致的环境变量命名规范
- **无警告配置**: 确保所有配置文件无语法警告
- **版本兼容**: 配置模板适配最新版本的技术栈要求

#### 版本更新和维护
- **配置模板更新**: 定期更新配置模板以适应新版本
- **兼容性测试**: 验证新版本配置的兼容性
- **官方文档跟踪**: 持续跟踪官方文档变更

## 项目状态

### 当前版本
- **版本**: v1.0.0

### 主要特性（基于用户偏好实现）
- ✅ 智能证书管理系统（软链接机制，配额保护）
- ✅ Let's Encrypt配额保护（用户重点关注）
- ✅ Docker镜像优化（V2标准，中文日志）
- ✅ 跨平台兼容性（Ubuntu 和 Debian支持）
- ✅ RouterOS网络集成（Legacy API支持）
- ✅ 完整的监控和备份系统（用户要求功能）
- ✅ SQLite数据库支持（用户偏好）
- ✅ 中文界面和日志（用户语言偏好）
- ✅ 可选安全功能（防火墙/fail2ban用户提示）
- ✅ 独立子域名证书管理
- ✅ TURN域名优化（无web服务配置）
- ✅ 证书软链接机制（用户偏好）
- ✅ Docker Compose V2标准（用户偏好）
- ✅ 无警告配置文件（用户偏好）
- ✅ 环境变量一致性（用户偏好）
- ✅ internal目录整洁（独立部署包所包含的所有必要文件，无临时文件）

### 技术债务
- 无重大技术债务
- 所有已知问题已解决
- 代码质量和测试覆盖率达标

## 后续发展

### 计划功能（考虑用户偏好）
- 支持更多数据库后端（保持SQLite为默认）
- 增强的监控和告警系统（中文通知）
- Web管理界面（中文界面）
- 集群部署支持
- 更多RouterOS功能集成
- 证书管理的进一步优化

### 维护计划（基于用户需求和2025年7月技术标准）
- 定期更新Docker镜像（保持V2标准，跟踪官方最新版本）
- 跟踪Synapse版本更新（基于官方发布周期）
- 持续改进证书管理机制（软链接优化，Let's Encrypt最新API）
- 增强文档和用户指南（中文优先，官方文档同步）
- 保持internal目录的整洁性
- 持续优化Let's Encrypt配额保护机制
- 定期验证技术栈版本兼容性
- 更新Python虚拟环境和依赖管理

### 用户偏好的长期维护
- 保持中文优先的用户体验
- 维护SQLite作为默认数据库选择
- 持续优化证书软链接机制
- 保持Docker Compose V2标准合规
- 维护RouterOS 7.x API兼容性（跟踪官方更新）
- 支持Debian 12 Python环境管理

### 技术栈更新策略
- **季度检查**: 每季度检查所有组件的最新版本
- **安全更新**: 及时应用安全相关的版本更新
- **兼容性测试**: 新版本部署前进行充分的兼容性测试
- **回滚机制**: 提供版本回滚和故障恢复机制
- **文档同步**: 确保文档与最新版本保持同步
