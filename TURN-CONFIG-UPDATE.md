# TURN服务配置更新说明

## 更新内容

根据您的建议，我们采用了**方案3**，移除了nginx中TURN服务器的Web配置部分。

## 更新理由

1. **专业性**: TURN服务本质上是UDP/TCP协议服务，不需要HTTP Web界面
2. **简化配置**: 减少不必要的nginx server块，降低配置复杂性
3. **安全性**: 避免暴露TURN服务信息到Web界面
4. **资源优化**: 减少nginx配置，节省系统资源

## 当前配置

### ✅ 保留的功能
- **TURN域名SSL证书**: 继续申请和管理 `turn.example.com` 的SSL证书
- **TURN服务端口**: 3478 (STUN/TURN) 和 5349 (TURNS) 正常工作
- **证书存储**: 证书文件存储在 `/opt/matrix/certs/turn/` 目录

### ❌ 移除的功能
- **nginx Web配置**: 不再为TURN域名配置nginx server块
- **Web状态页面**: 无法通过 `https://turn.example.com:8443` 访问
- **Web健康检查**: 移除了TURN域名的Web健康检查端点

## 服务访问方式

### Matrix服务
- **Web访问**: `https://matrix.example.com:8443`
- **管理界面**: `https://matrix.example.com:8443/_synapse/admin`

### TURN服务
- **STUN/TURN**: `turn://turn.example.com:3478`
- **TURN TLS**: `turns://turn.example.com:5349`
- **UDP媒体**: `turn.example.com:65335-65535`

### 基础域名
- **重定向**: `https://example.com:8443` → `https://matrix.example.com:8443`

## 客户端配置

Matrix客户端会自动使用配置的TURN服务器，无需手动配置：

```yaml
# Synapse配置中的TURN设置
turn_uris:
  - "turn:turn.example.com:3478?transport=udp"
  - "turn:turn.example.com:3478?transport=tcp"
  - "turns:turn.example.com:5349?transport=tcp"
```

## 证书管理

TURN域名的SSL证书仍然会被申请和管理：

```bash
# 证书申请
/root/.acme.sh/acme.sh --issue --dns dns_cf -d turn.example.com

# 证书安装
/root/.acme.sh/acme.sh --install-cert -d turn.example.com \
    --cert-file /opt/matrix/certs/turn/cert.pem \
    --key-file /opt/matrix/certs/turn/key.pem \
    --fullchain-file /opt/matrix/certs/turn/fullchain.pem
```

## 监控和状态检查

### TURN服务状态检查
```bash
# 检查TURN服务状态
docker logs matrix-coturn

# 测试TURN连接
turnutils_uclient -t turn.example.com -p 3478

# 检查端口监听
netstat -tuln | grep -E ':(3478|5349)'
```

### 健康检查脚本
健康检查脚本已更新，会检查TURN服务的端口连通性，但不再检查Web界面。

## 路由器配置

端口转发配置保持不变：

```bash
# STUN/TURN端口
3478 -> 3478 (TCP/UDP)
5349 -> 5349 (TCP)

# UDP媒体端口范围
65335-65535 -> 65335-65535 (UDP)
```

## 优势总结

1. **更专业**: TURN服务专注于其核心功能
2. **更简洁**: nginx配置更加清晰
3. **更安全**: 不暴露TURN服务信息
4. **更高效**: 减少不必要的Web服务

## 兼容性

- ✅ **完全兼容**: 所有Matrix客户端正常工作
- ✅ **TURN功能**: 语音/视频通话功能不受影响
- ✅ **证书管理**: SSL证书自动续期正常工作
- ✅ **监控脚本**: 健康检查和备份脚本正常工作

这个更新使得整个系统更加专业和高效，TURN服务专注于其核心的媒体中继功能，而Web访问完全通过Matrix域名处理。
