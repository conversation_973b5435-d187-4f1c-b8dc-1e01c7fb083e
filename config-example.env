# Matrix Synapse 配置示例文件
# 复制此文件为 config.env 并根据您的环境进行修改

# ================================
# 基本配置
# ================================

# 域名配置 (必填)
DOMAIN_NAME=matrix.example.com

# 安装路径 (可选，默认: /opt/matrix)
INSTALL_PATH=/opt/matrix

# ================================
# 端口配置
# ================================

# HTTPS端口 (默认: 8443)
# 这是外部访问的端口，需要在路由器上配置端口转发
HTTPS_PORT=8443

# HTTP端口 (默认: 8080)
# 用于HTTP重定向和Let's Encrypt验证
HTTP_PORT=8080

# Coturn UDP端口范围 (默认: 65335-65535)
# 用于语音/视频通话的媒体传输
COTURN_PORT_START=65335
COTURN_PORT_END=65535

# ================================
# SSL证书配置
# ================================

# 证书邮箱 (默认: acme@域名)
CERT_EMAIL=<EMAIL>

# Cloudflare API Token (必填)
# 需要Zone:Zone:Read和Zone:DNS:Edit权限
CF_TOKEN=your_cloudflare_api_token_here

# ================================
# Matrix服务器配置
# ================================

# 是否启用用户注册 (true/false，默认: false)
ENABLE_REGISTRATION=false

# 是否启用联邦功能 (true/false，默认: true)
ENABLE_FEDERATION=true

# ================================
# 安全配置 (可选)
# ================================

# 是否配置防火墙 (true/false，默认: false)
SETUP_FIREWALL=false

# 是否安装fail2ban (true/false，默认: false)
INSTALL_FAIL2BAN=false

# ================================
# RouterOS集成配置 (可选)
# ================================

# 是否启用RouterOS集成 (true/false，默认: false)
SETUP_ROUTEROS=false

# RouterOS设备IP地址
ROUTEROS_IP=***********

# RouterOS API用户名
ROUTEROS_USER=api

# RouterOS API密码
ROUTEROS_PASS=api

# WAN接口名称 (通常是WAN、ether1等)
WAN_INTERFACE=WAN

# ================================
# 备份配置 (可选)
# ================================

# 备份保留天数 (默认: 30)
BACKUP_RETENTION_DAYS=30

# 是否启用远程备份 (true/false，默认: false)
REMOTE_BACKUP_ENABLED=false

# 远程备份主机
REMOTE_BACKUP_HOST=backup.example.com

# 远程备份用户
REMOTE_BACKUP_USER=backup

# 远程备份路径
REMOTE_BACKUP_PATH=/backups/matrix

# ================================
# 监控和通知配置 (可选)
# ================================

# 是否启用通知 (true/false，默认: false)
NOTIFICATION_ENABLED=false

# Webhook通知URL (Slack、Discord等)
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# 邮件通知地址
EMAIL_TO=<EMAIL>

# 磁盘使用率阈值 (百分比，默认: 80)
DISK_USAGE_THRESHOLD=80

# 内存使用率阈值 (百分比，默认: 85)
MEMORY_USAGE_THRESHOLD=85

# 证书过期提醒天数 (默认: 30)
CERT_EXPIRY_DAYS=30

# ================================
# 高级配置 (通常不需要修改)
# ================================

# Docker Compose项目名称
COMPOSE_PROJECT_NAME=matrix

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 是否启用调试模式 (true/false，默认: false)
DEBUG_MODE=false

# ================================
# 使用说明
# ================================

# 1. 复制此文件为 config.env:
#    cp config-example.env config.env

# 2. 编辑 config.env 文件，填入您的实际配置

# 3. 运行部署脚本:
#    sudo ./deploy.sh

# 4. 或者使用环境变量运行:
#    sudo env $(cat config.env | xargs) ./deploy.sh

# ================================
# 重要提醒
# ================================

# 1. 域名配置:
#    - 确保域名已正确解析到您的公网IP
#    - 如果使用Cloudflare，确保DNS记录存在

# 2. 路由器配置:
#    - 配置端口转发: 80->8080, 443->8443, 3478->3478
#    - 配置UDP端口转发: 65335-65535->65335-65535
#    - 如果使用RouterOS集成，确保API服务已启用

# 3. 防火墙配置:
#    - 如果启用防火墙，确保允许必要的端口
#    - SSH端口(22)会自动允许

# 4. 安全建议:
#    - 使用强密码
#    - 定期更新系统
#    - 监控日志文件
#    - 定期备份数据

# 5. 性能优化:
#    - 对于大型部署，考虑使用PostgreSQL替代SQLite
#    - 根据用户数量调整资源限制
#    - 定期清理旧媒体文件

# ================================
# 故障排除
# ================================

# 如果遇到问题，请检查:
# 1. 日志文件: /opt/matrix/logs/
# 2. 服务状态: docker-compose ps
# 3. 网络连通性: curl -I https://域名:端口
# 4. DNS解析: nslookup 域名
# 5. 证书状态: openssl x509 -in /opt/matrix/certs/fullchain.pem -noout -dates

# 更多帮助请参考:
# - README.md: 项目概述
# - manual-guide.md: 详细部署指南
# - 项目GitHub页面: https://github.com/your-repo/matrix-deploy
