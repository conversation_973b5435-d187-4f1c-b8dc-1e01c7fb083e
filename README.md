# Matrix Synapse 自动化部署项目

这是一个完整的Matrix Synapse自动化部署解决方案，专为内网环境设计，支持自定义端口、证书管理、RouterOS集成等功能。

## 项目特性

- **内网友好**: 支持自定义端口映射，适用于ISP封锁80/443端口的环境
- **证书自动化**: 集成acme.sh和Cloudflare DNS API，自动申请和续期SSL证书
- **Docker部署**: 使用Docker Compose部署Synapse和Coturn服务
- **SQLite优化**: 使用官方推荐的SQLite配置和优化参数
- **RouterOS集成**: 自动获取公网IP并更新Coturn配置
- **备份监控**: 包含自动备份和健康检查功能
- **初学者友好**: 详细的交互式安装和完整的手动部署指南

## 系统要求

- **操作系统**: Debian 12
- **内存**: 8GB RAM
- **网络**: 内网环境，支持端口转发
- **域名**: 自定义域名，配置Cloudflare DNS

## 快速开始

### 自动化部署

```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/your-repo/matrix-deploy/main/deploy.sh
chmod +x deploy.sh

# 运行自动化部署
sudo ./deploy.sh
```

### 手动部署

详细的手动部署指南请参考 [manual-guide.md](manual-guide.md)

## 文件结构

```
matrix/
├── deploy.sh                 # 主要自动化部署脚本
├── manual-guide.md          # 详细手动部署指南
├── docker-compose.yml       # Docker Compose配置
├── configs/                 # 配置文件模板
│   ├── homeserver.yaml.template
│   ├── coturn.conf.template
│   └── nginx.conf.template
├── scripts/                 # 辅助脚本
│   ├── backup.sh           # 备份脚本
│   ├── health-check.sh     # 健康检查脚本
│   └── certificate-manager.sh
└── routeros/               # RouterOS集成
    ├── ip-monitor.py       # IP监控脚本
    └── requirements.txt    # Python依赖
```

## 配置说明

### 域名配置

- **基础域名**: example.com (重定向到Matrix)
- **Matrix子域名**: matrix.example.com (可自定义前缀)
- **TURN子域名**: turn.example.com (可自定义前缀，仅用于TURN服务)
- **独立SSL证书**: 每个子域名使用独立证书

### 端口配置

- **Synapse HTTP**: 8008 (内部)
- **HTTPS**: 8443 (可配置，ISP封禁443端口的解决方案)
- **HTTP**: 8080 (可配置，ISP封禁80端口的解决方案)
- **Coturn STUN/TURN**: 3478 (直接访问，无Web界面)
- **Coturn TLS**: 5349 (TURNS，直接访问)
- **Coturn UDP**: 65335-65535 (媒体中继)

### 证书管理

使用acme.sh和Cloudflare DNS API自动管理SSL证书：

- **独立证书**: 每个子域名使用独立SSL证书
- **DNS验证**: 通过Cloudflare DNS API验证，无需暴露80/443端口
- **自动续期**: 每60天检查一次
- **默认邮箱**: `acme@[base-domain]`
- **支持的域名**:
  - matrix.example.com (Matrix服务)
  - turn.example.com (TURN服务)
  - example.com (基础域名，用于重定向)

### RouterOS集成

- 自动获取WAN口公网IP
- 检测IP变化并更新Coturn配置
- 支持RouterOS Legacy API

## 安全配置

### 可选安全功能

- **防火墙**: UFW配置，默认关闭特定端口
- **fail2ban**: 防止暴力攻击，可选安装
- **SSL/TLS**: 强制HTTPS，现代加密套件

### 网络安全

- 内网部署，降低暴露风险
- RouterOS Legacy API仅限内网使用
- 定期安全更新和监控

## 备份和监控

### 自动备份

- **数据备份**: SQLite数据库、配置文件、证书
- **备份频率**: 每日自动备份
- **保留策略**: 保留30天备份

### 健康检查

- **服务状态**: 检查Synapse和Coturn服务
- **端口连通性**: 验证网络连接
- **证书有效期**: 监控SSL证书状态
- **磁盘空间**: 监控存储使用情况

## 故障排除

### 常见问题

1. **端口无法访问**: 检查路由器端口转发配置
2. **证书申请失败**: 验证Cloudflare API Token权限
3. **服务启动失败**: 查看Docker日志和系统日志
4. **RouterOS连接失败**: 检查API用户权限和网络连接

### 日志位置

- **Synapse日志**: `/opt/matrix/logs/homeserver.log`
- **Coturn日志**: `/opt/matrix/logs/coturn.log`
- **部署日志**: `/opt/matrix/logs/deploy.log`
- **备份日志**: `/opt/matrix/logs/backup.log`

## 支持和贡献

### 获取帮助

- 查看详细文档: [manual-guide.md](manual-guide.md)
- 检查常见问题解答
- 提交Issue报告问题

### 贡献代码

欢迎提交Pull Request改进项目：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 更新日志

### v1.0.0 (2025-07-13)

- 初始版本发布
- 支持Debian 12自动化部署
- 集成acme.sh证书管理
- RouterOS API集成
- 完整的备份和监控功能
