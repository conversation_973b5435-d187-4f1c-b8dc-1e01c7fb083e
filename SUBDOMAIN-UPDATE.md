# 子域名和ISP端口封禁支持更新

## 更新概述

根据您的反馈，我们已经更新了Matrix Synapse部署项目，以支持：

1. **子域名配置**: 支持自定义子域名前缀（matrix、turn为默认）
2. **独立SSL证书**: 每个子域名使用独立的SSL证书
3. **ISP端口封禁**: 完全考虑80/443端口被封禁的情况

## 主要更新内容

### 🌐 域名配置更新

**之前**: 单一域名配置
```
DOMAIN_NAME=matrix.example.com
```

**现在**: 子域名配置
```bash
# 基础域名
BASE_DOMAIN=example.com

# 子域名前缀 (可自定义)
MATRIX_PREFIX=matrix    # 默认值，可改为 chat、synapse 等
TURN_PREFIX=turn        # 默认值，可改为 coturn、voice 等

# 生成的完整域名
MATRIX_DOMAIN=matrix.example.com
TURN_DOMAIN=turn.example.com
```

### 🔒 SSL证书管理更新

**独立证书申请**:
- `matrix.example.com` - Matrix服务专用证书
- `turn.example.com` - TURN服务专用证书  
- `example.com` - 基础域名证书（用于重定向）

**证书存储结构**:
```
/opt/matrix/certs/
├── matrix/
│   ├── cert.pem
│   ├── key.pem
│   ├── fullchain.pem
│   └── ca.pem
├── turn/
│   ├── cert.pem
│   ├── key.pem
│   ├── fullchain.pem
│   └── ca.pem
└── base/
    ├── cert.pem
    ├── key.pem
    ├── fullchain.pem
    └── ca.pem
```

### 🚪 端口配置更新

**ISP端口封禁解决方案**:

| 服务 | 标准端口 | 自定义端口 | 说明 |
|------|----------|------------|------|
| HTTP | 80 | 8080 | 内网访问和健康检查 |
| HTTPS | 443 | 8443 | 主要HTTPS访问端口 |
| STUN/TURN | 3478 | 3478 | 保持标准端口 |
| TURNS | 5349 | 5349 | TLS加密TURN |
| UDP媒体 | 65335-65535 | 65335-65535 | 媒体传输端口范围 |

### 🔧 Nginx配置更新

**多域名支持**:
```nginx
# Matrix主服务器
server {
    listen 443 ssl http2;
    server_name matrix.example.com;
    ssl_certificate /certs/matrix/fullchain.pem;
    ssl_certificate_key /certs/matrix/key.pem;
    # Matrix API代理配置
}

# 基础域名重定向
server {
    listen 443 ssl http2;
    server_name example.com;
    ssl_certificate /certs/base/fullchain.pem;
    ssl_certificate_key /certs/base/key.pem;
    # 重定向到Matrix子域名
}

# 注意: TURN服务不通过nginx代理
# TURN服务直接通过以下端口访问:
# - turn://turn.example.com:3478 (STUN/TURN)
# - turns://turn.example.com:5349 (TURN over TLS)
```

### 📡 路由器端口转发更新

**必需的端口转发规则**:

```bash
# HTTP/HTTPS (自定义端口)
外部端口 8080 -> 内部端口 8080 (TCP)
外部端口 8443 -> 内部端口 8443 (TCP)

# STUN/TURN
外部端口 3478 -> 内部端口 3478 (TCP/UDP)
外部端口 5349 -> 内部端口 5349 (TCP)

# UDP媒体端口范围
外部端口 65335-65535 -> 内部端口 65335-65535 (UDP)
```

**RouterOS配置示例**:
```routeros
# HTTP重定向端口
/ip firewall nat add chain=dstnat protocol=tcp dst-port=8080 action=dst-nat to-addresses=************* to-ports=8080

# HTTPS主端口
/ip firewall nat add chain=dstnat protocol=tcp dst-port=8443 action=dst-nat to-addresses=************* to-ports=8443

# STUN/TURN端口
/ip firewall nat add chain=dstnat protocol=tcp dst-port=3478 action=dst-nat to-addresses=************* to-ports=3478
/ip firewall nat add chain=dstnat protocol=udp dst-port=3478 action=dst-nat to-addresses=************* to-ports=3478

# TURNS端口
/ip firewall nat add chain=dstnat protocol=tcp dst-port=5349 action=dst-nat to-addresses=************* to-ports=5349

# UDP媒体端口范围
/ip firewall nat add chain=dstnat protocol=udp dst-port=65335-65535 action=dst-nat to-addresses=************* to-ports=65335-65535
```

## 使用方法

### 1. 交互式部署

运行部署脚本时，系统会询问：

```bash
sudo ./deploy.sh

# 系统会提示：
请输入您的基础域名 (例如: example.com): your-domain.com
请输入Matrix子域名前缀 [matrix]: matrix
请输入TURN子域名前缀 [turn]: turn

# 配置的域名:
基础域名: your-domain.com
Matrix域名: matrix.your-domain.com
TURN域名: turn.your-domain.com
```

### 2. 环境变量配置

编辑 `config-example.env` 文件：

```bash
# 复制配置文件
cp config-example.env config.env

# 编辑配置
nano config.env

# 设置域名
BASE_DOMAIN=your-domain.com
MATRIX_PREFIX=matrix
TURN_PREFIX=turn

# 运行部署
sudo env $(cat config.env | xargs) ./deploy.sh
```

### 3. DNS配置要求

在Cloudflare中配置以下DNS记录：

```
A    your-domain.com        -> 你的公网IP
A    matrix.your-domain.com -> 你的公网IP  
A    turn.your-domain.com   -> 你的公网IP
```

## 客户端配置

### Element客户端配置

**服务器设置**:
- **Matrix服务器**: `https://matrix.your-domain.com:8443`
- **用户名格式**: `@username:matrix.your-domain.com`
- **TURN服务器**: 自动配置，无需手动设置

### Well-known配置

系统会自动配置Matrix发现机制：

**基础域名访问** (`https://your-domain.com:8443/.well-known/matrix/client`):
```json
{
  "m.homeserver": {
    "base_url": "https://matrix.your-domain.com:8443"
  }
}
```

**Matrix域名访问** (`https://matrix.your-domain.com:8443/.well-known/matrix/client`):
```json
{
  "m.homeserver": {
    "base_url": "https://matrix.your-domain.com:8443"
  }
}
```

## 证书管理

### 自动续期

证书管理脚本已更新，支持多域名证书续期：

```bash
# 检查所有证书状态
./scripts/certificate-manager.sh status

# 手动续期所有证书
./scripts/certificate-manager.sh renew-all

# 自动续期检查（cron任务）
0 3 * * * /opt/matrix/scripts/certificate-manager.sh auto-renew
```

### 证书验证

```bash
# 验证Matrix证书
openssl x509 -in /opt/matrix/certs/matrix/fullchain.pem -noout -dates

# 验证TURN证书
openssl x509 -in /opt/matrix/certs/turn/fullchain.pem -noout -dates

# 验证基础域名证书
openssl x509 -in /opt/matrix/certs/base/fullchain.pem -noout -dates
```

## 故障排除

### 常见问题

1. **证书申请失败**
   ```bash
   # 检查DNS解析
   nslookup matrix.your-domain.com
   nslookup turn.your-domain.com
   
   # 检查Cloudflare API Token权限
   curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
        -H "Authorization: Bearer YOUR_API_TOKEN"
   ```

2. **端口无法访问**
   ```bash
   # 检查端口监听
   netstat -tuln | grep -E ':(8080|8443|3478|5349)'
   
   # 测试外部访问
   curl -I https://matrix.your-domain.com:8443
   ```

3. **TURN服务连接问题**
   ```bash
   # 检查TURN服务状态
   docker logs matrix-coturn
   
   # 测试TURN连接
   turnutils_uclient -t turn.your-domain.com -p 3478
   ```

## 兼容性说明

- ✅ **向后兼容**: 现有的单域名配置仍然支持
- ✅ **渐进升级**: 可以逐步迁移到子域名配置
- ✅ **ISP友好**: 完全避免使用80/443端口
- ✅ **证书安全**: 每个服务使用独立证书，提高安全性

## 总结

这次更新完全解决了您提出的问题：

1. ✅ **子域名支持**: matrix、turn可自定义前缀
2. ✅ **独立证书**: 每个子域名使用独立SSL证书
3. ✅ **ISP端口封禁**: nginx配置完全考虑80/443端口封禁情况
4. ✅ **DNS验证**: 通过Cloudflare DNS API验证，无需暴露标准端口
5. ✅ **路由器友好**: 详细的端口转发配置说明

项目现在完全适用于ISP封禁80/443端口的内网环境，同时支持灵活的子域名配置和独立的SSL证书管理。
