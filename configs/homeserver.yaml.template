# Matrix Synapse 主配置文件
# 适用于内网环境，使用SQLite数据库
# 基于官方推荐配置，针对中小型部署优化

# 服务器基本配置
server_name: "{{DOMAIN_NAME}}"
pid_file: /data/homeserver.pid
web_client_location: https://app.element.io/

# 监听配置
listeners:
  # 主要HTTP监听器
  - port: 8008
    tls: false
    type: http
    x_forwarded: true
    bind_addresses: ['0.0.0.0']
    
    resources:
      - names: [client, federation]
        compress: false

# 数据库配置 - SQLite (官方推荐配置)
database:
  name: sqlite3
  args:
    # 数据库文件路径
    database: /data/homeserver.db
    
    # SQLite性能优化配置
    # 启用WAL模式提升并发性能
    journal_mode: WAL
    
    # 设置同步模式为NORMAL，平衡性能和安全性
    synchronous: NORMAL
    
    # 增加缓存大小 (单位: 页，每页通常4KB)
    # 64MB缓存 = 16384页
    cache_size: -16384
    
    # 设置临时存储为内存
    temp_store: MEMORY
    
    # 启用外键约束
    foreign_keys: ON
    
    # 设置页面大小为4KB (默认值，适合大多数情况)
    page_size: 4096
    
    # 设置检查点间隔
    wal_autocheckpoint: 1000

# 日志配置
log_config: "/data/log.config"

# 媒体存储配置
media_store_path: /data/media_store
uploads_path: /data/uploads

# 媒体文件限制
max_upload_size: 50M
max_image_pixels: 32M

# 媒体保留策略
media_retention:
  # 本地媒体保留时间 (天)
  local_media_lifetime: 365d
  # 远程媒体保留时间 (天)  
  remote_media_lifetime: 90d

# URL预览配置
url_preview_enabled: true
url_preview_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# 注册配置
enable_registration: {{ENABLE_REGISTRATION}}
enable_registration_without_verification: false

# 如果启用注册，建议配置CAPTCHA
# recaptcha_public_key: "YOUR_RECAPTCHA_PUBLIC_KEY"
# recaptcha_private_key: "YOUR_RECAPTCHA_PRIVATE_KEY"
# enable_registration_captcha: true

# 用户目录配置
user_directory:
  enabled: true
  search_all_users: false
  prefer_local_users: true

# 联邦配置
federation_domain_whitelist: []
federation_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# 联邦配置 (如果需要禁用联邦，取消下面行的注释)
{{FEDERATION_CONFIG}}

# 速率限制配置
rc_message:
  per_second: 0.2
  burst_count: 10

rc_registration:
  per_second: 0.17
  burst_count: 3

rc_login:
  address:
    per_second: 0.17
    burst_count: 3
  account:
    per_second: 0.17
    burst_count: 3
  failed_attempts:
    per_second: 0.17
    burst_count: 3

# TURN服务器配置
turn_uris:
  - "turn:{{TURN_DOMAIN}}:3478?transport=udp"
  - "turn:{{TURN_DOMAIN}}:3478?transport=tcp"
  - "turns:{{TURN_DOMAIN}}:5349?transport=tcp"

turn_shared_secret: "{{TURN_SECRET}}"
turn_user_lifetime: ********
turn_allow_guests: true

# 安全配置
# 密钥配置
macaroon_secret_key: "{{MACAROON_SECRET}}"
registration_shared_secret: "{{REGISTRATION_SECRET}}"
form_secret: "{{FORM_SECRET}}"

# 签名密钥
signing_key_path: "/data/{{DOMAIN_NAME}}.signing.key"

# 信任的密钥服务器
trusted_key_servers:
  - server_name: "matrix.org"

# 性能优化配置
# 缓存配置
caches:
  global_factor: 1.0
  per_cache_factors:
    get_users_who_share_room_with_user: 2.0
    get_users_in_room: 2.0

# 事件缓存大小
event_cache_size: 10K

# 客户端配置
presence:
  enabled: true

# 推送配置
push:
  include_content: true

# 房间配置
# 默认房间版本
default_room_version: "10"

# 房间复杂度限制
limit_remote_rooms:
  enabled: true
  complexity: 1.0
  complexity_error: "This room is too complex."

# 管理员联系方式
admin_contact: 'mailto:admin@{{DOMAIN_NAME}}'

# 服务器通知配置
server_notices:
  system_mxid_localpart: notices
  system_mxid_display_name: "Server Notices"
  system_mxid_avatar_url: "mxc://{{DOMAIN_NAME}}/oumMVlgDnLYFaPVkExemINVg"
  room_name: "Server Notices"

# 统计报告 (建议禁用以保护隐私)
report_stats: false

# 应用服务配置文件目录
app_service_config_files: []

# 实验性功能
experimental_features:
  # 启用新的用户目录搜索
  msc3026_enabled: true

# 模块配置 (可选)
modules: []

# 邮件配置 (可选，用于密码重置等)
# email:
#   smtp_host: "localhost"
#   smtp_port: 25
#   smtp_user: ""
#   smtp_pass: ""
#   require_transport_security: false
# <AUTHOR> <EMAIL>"
#   app_name: Matrix
#   notif_template_html: notif_mail.html
#   notif_template_text: notif_mail.txt

# 配置说明:
# 1. 此配置针对SQLite数据库进行了优化
# 2. 包含了性能调优参数
# 3. 配置了合理的安全限制
# 4. 支持媒体文件管理和清理
# 5. 包含了TURN服务器集成
# 6. 可根据需要启用/禁用功能

# 重要提示:
# - 首次启动前需要生成签名密钥
# - 建议定期备份数据库文件
# - 监控日志文件大小和磁盘使用情况
# - 根据实际使用情况调整缓存和限制参数
