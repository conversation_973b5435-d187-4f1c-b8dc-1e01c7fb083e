# Coturn TURN/STUN 服务器配置
# 适用于Matrix Synapse内网部署
# 支持动态IP更新和RouterOS集成

# 基本配置
# 监听端口配置
listening-port=3478
tls-listening-port=5349

# 监听地址 (0.0.0.0表示监听所有接口)
listening-ip=0.0.0.0

# 外部IP地址 (将由RouterOS脚本动态更新)
external-ip={{EXTERNAL_IP}}

# 中继端口范围 (用于媒体传输)
min-port={{COTURN_PORT_START}}
max-port={{COTURN_PORT_END}}

# 认证配置
# 使用静态认证密钥 (与Synapse配置保持一致)
use-auth-secret
static-auth-secret={{TURN_SECRET}}

# 域名配置
realm={{DOMAIN_NAME}}
server-name={{DOMAIN_NAME}}

# SSL/TLS证书配置
cert=/certs/turn/fullchain.pem
pkey=/certs/turn/key.pem

# 协议配置
# 启用STUN协议
stun-only=false

# 启用TURN协议
no-stun=false

# 支持的协议
no-udp=false
no-tcp=false
no-tls=false
no-dtls=false

# 安全配置
# 禁用不安全的传统认证
no-auth=false

# 启用指纹认证
fingerprint

# 禁用多播DNS
no-multicast-peers

# 网络配置
# 允许的IP范围 (内网地址)
allowed-peer-ip=10.0.0.0-1*************
allowed-peer-ip=**********-**************
allowed-peer-ip=***********-***************

# 禁止的IP范围 (防止访问敏感内网服务)
denied-peer-ip=0.0.0.0-*************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************
denied-peer-ip=*********-***************

# IPv6配置 (如果需要)
# denied-peer-ip=::1
# denied-peer-ip=fe80::-febf:ffff:ffff:ffff:ffff:ffff:ffff:ffff

# 性能配置
# 用户配额 (每个用户最大连接数)
user-quota=12

# 总配额 (服务器最大连接数)
total-quota=1200

# 会话超时 (秒)
max-bps=64000

# 日志配置
# 日志级别: 0=ERROR, 1=WARN, 2=INFO, 3=DEBUG
verbose

# 日志文件
log-file=/var/log/coturn/turnserver.log

# 简单日志格式
simple-log

# 数据库配置 (可选，用于持久化用户数据)
# 对于小型部署，可以不使用数据库
# userdb=/var/lib/coturn/turndb

# 进程配置
# 以指定用户运行
# user=turnserver

# 进程ID文件
pidfile=/var/run/turnserver.pid

# 高级配置
# 启用REST API (用于动态配置)
# web-admin
# web-admin-ip=127.0.0.1
# web-admin-port=8080

# 移动网络优化
mobility

# 启用带宽限制
# bps-capacity=0

# 客户端配置
# 默认客户端超时
default-users-quota=12

# 会话生命周期
max-allocate-lifetime=3600

# 通道生命周期
channel-lifetime=600

# 权限生命周期
permission-lifetime=300

# 网络接口配置
# 如果有多个网络接口，可以指定使用的接口
# relay-device=eth0

# 监控和统计
# 启用统计信息收集
# stats
# stats-file=/var/log/coturn/stats.log

# CLI配置 (已禁用，如需启用请移除下面的no-cli配置)
# 启用CLI接口用于管理
# cli-ip=127.0.0.1
# cli-port=5766
# cli-password=your_secure_password

# 安全增强
# 禁用不必要的功能
no-cli
no-web-admin

# 启用安全检查
check-origin-consistency

# 防止某些类型的攻击
no-loopback-peers
no-multicast-peers

# 配置说明:
# 1. 此配置适用于内网环境的Matrix部署
# 2. 外部IP将由RouterOS脚本动态更新
# 3. 使用与Synapse相同的认证密钥
# 4. 配置了合理的安全限制
# 5. 支持UDP和TCP协议
# 6. 包含了性能优化参数

# 端口说明:
# - 3478: STUN/TURN标准端口
# - 5349: TURN over TLS端口
# - {{COTURN_PORT_START}}-{{COTURN_PORT_END}}: 媒体中继端口范围

# 重要提示:
# - 确保防火墙允许相关端口
# - 定期检查日志文件
# - 监控连接数和带宽使用
# - 根据实际负载调整配额参数

# RouterOS集成说明:
# - external-ip将由ip-monitor.py脚本自动更新
# - 当检测到IP变化时，会重启Coturn服务
# - 确保RouterOS API用户有足够权限读取WAN接口信息
