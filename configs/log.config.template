# Matrix Synapse 日志配置
# 适用于生产环境，包含日志轮转和性能优化

version: 1

formatters:
  # 精确格式 - 包含详细信息
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  # 简洁格式 - 用于一般日志
  brief:
    format: '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  # JSON格式 - 便于日志分析
  json:
    format: '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s", "module": "%(module)s", "line": %(lineno)d}'
    datefmt: '%Y-%m-%dT%H:%M:%S'

handlers:
  # 主日志文件
  file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: precise
    filename: /data/logs/homeserver.log
    when: midnight
    interval: 1
    backupCount: 30
    encoding: utf8
    
  # 错误日志文件
  error_file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: precise
    filename: /data/logs/homeserver-error.log
    when: midnight
    interval: 1
    backupCount: 30
    encoding: utf8
    level: ERROR
  
  # 访问日志文件
  access_file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: brief
    filename: /data/logs/access.log
    when: midnight
    interval: 1
    backupCount: 7
    encoding: utf8
  
  # 联邦日志文件
  federation_file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: brief
    filename: /data/logs/federation.log
    when: midnight
    interval: 1
    backupCount: 7
    encoding: utf8
  
  # 控制台输出 (Docker环境)
  console:
    class: logging.StreamHandler
    formatter: brief
    stream: ext://sys.stdout
  
  # 缓冲处理器 - 提升性能
  buffer:
    class: logging.handlers.MemoryHandler
    capacity: 1000
    flushLevel: ERROR
    target: file

loggers:
  # Synapse主日志
  synapse:
    level: INFO
    handlers: [file, console]
    propagate: false
  
  # HTTP访问日志
  synapse.access.http:
    level: INFO
    handlers: [access_file]
    propagate: false
  
  # 联邦日志
  synapse.federation:
    level: INFO
    handlers: [federation_file]
    propagate: false
  
  # 数据库日志
  synapse.storage.SQL:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # 认证日志
  synapse.handlers.auth:
    level: INFO
    handlers: [file]
    propagate: false
  
  # 媒体日志
  synapse.rest.media:
    level: INFO
    handlers: [file]
    propagate: false
  
  # 推送日志
  synapse.push:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # 应用服务日志
  synapse.appservice:
    level: INFO
    handlers: [file]
    propagate: false
  
  # 事件日志
  synapse.events:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # 性能相关日志
  synapse.util.metrics:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # 第三方库日志
  # Twisted日志
  twisted:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # PIL图像处理日志
  PIL:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # HTTP客户端日志
  synapse.http.client:
    level: WARNING
    handlers: [file]
    propagate: false
  
  # 缓存日志
  synapse.util.caches:
    level: WARNING
    handlers: [file]
    propagate: false

# 根日志配置
root:
  level: INFO
  handlers: [file, error_file, console]

# 禁用某些噪音日志
disable_existing_loggers: false

# 配置说明:
# 1. 主日志文件每天轮转，保留30天
# 2. 错误日志单独记录，便于问题排查
# 3. 访问日志和联邦日志分别记录
# 4. 控制台输出适用于Docker环境
# 5. 数据库和性能日志级别较高，减少噪音
# 6. 支持JSON格式便于日志分析

# 日志级别说明:
# - DEBUG: 详细调试信息
# - INFO: 一般信息
# - WARNING: 警告信息
# - ERROR: 错误信息
# - CRITICAL: 严重错误

# 文件说明:
# - homeserver.log: 主要日志文件
# - homeserver-error.log: 错误日志
# - access.log: HTTP访问日志
# - federation.log: 联邦相关日志

# 性能优化:
# - 使用TimedRotatingFileHandler自动轮转
# - 缓冲处理器减少I/O操作
# - 合理设置日志级别避免过多输出
# - 分类记录不同类型的日志

# 维护建议:
# - 定期检查日志文件大小
# - 监控磁盘空间使用
# - 根据需要调整保留天数
# - 使用日志分析工具处理大量日志
