#!/bin/bash

# Matrix Synapse 项目完整性测试脚本
# 验证所有必要文件和脚本的存在性和基本语法

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试函数
test_file_exists() {
    local file="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "检查文件: $description"
    
    if [[ -f "$file" ]]; then
        log_info "✓ $file 存在"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ $file 不存在"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

test_dir_exists() {
    local dir="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "检查目录: $description"
    
    if [[ -d "$dir" ]]; then
        log_info "✓ $dir 存在"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ $dir 不存在"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

test_script_syntax() {
    local script="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "检查脚本语法: $description"
    
    if [[ ! -f "$script" ]]; then
        log_error "✗ $script 不存在"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
    
    if bash -n "$script" 2>/dev/null; then
        log_info "✓ $script 语法正确"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ $script 语法错误"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

test_python_syntax() {
    local script="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "检查Python脚本语法: $description"
    
    if [[ ! -f "$script" ]]; then
        log_error "✗ $script 不存在"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
    
    if python3 -m py_compile "$script" 2>/dev/null; then
        log_info "✓ $script 语法正确"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ $script 语法错误"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

test_yaml_syntax() {
    local file="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "检查YAML语法: $description"
    
    if [[ ! -f "$file" ]]; then
        log_error "✗ $file 不存在"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
    
    if command -v python3 &> /dev/null; then
        if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            log_info "✓ $file YAML语法正确"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            return 0
        else
            log_warn "⚠ $file YAML语法可能有问题 (可能包含模板变量)"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            return 0
        fi
    else
        log_warn "⚠ 无法检查YAML语法 (python3未安装)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    fi
}

test_executable_permission() {
    local file="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "检查可执行权限: $description"
    
    if [[ ! -f "$file" ]]; then
        log_error "✗ $file 不存在"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
    
    if [[ -x "$file" ]]; then
        log_info "✓ $file 有可执行权限"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ $file 没有可执行权限"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# 主测试函数
run_tests() {
    echo ""
    log_info "开始Matrix Synapse项目完整性测试..."
    echo "=================================================="
    
    # 测试目录结构
    echo ""
    log_info "测试目录结构..."
    test_dir_exists "configs" "配置文件目录"
    test_dir_exists "scripts" "脚本目录"
    test_dir_exists "routeros" "RouterOS集成目录"
    test_dir_exists "logs" "日志目录"
    
    # 测试主要文件
    echo ""
    log_info "测试主要文件..."
    test_file_exists "README.md" "项目说明文档"
    test_file_exists "manual-guide.md" "手动部署指南"
    test_file_exists "docker-compose.yml" "Docker Compose配置"
    test_file_exists "config-example.env" "配置示例文件"
    
    # 测试脚本文件
    echo ""
    log_info "测试脚本文件..."
    test_file_exists "deploy.sh" "主部署脚本"
    test_file_exists "install.sh" "快速安装脚本"
    test_file_exists "scripts/backup.sh" "备份脚本"
    test_file_exists "scripts/health-check.sh" "健康检查脚本"
    test_file_exists "scripts/certificate-manager.sh" "证书管理脚本"
    
    # 测试配置模板
    echo ""
    log_info "测试配置模板..."
    test_file_exists "configs/homeserver.yaml.template" "Synapse配置模板"
    test_file_exists "configs/coturn.conf.template" "Coturn配置模板"
    test_file_exists "configs/nginx.conf.template" "Nginx配置模板"
    test_file_exists "configs/log.config.template" "日志配置模板"
    
    # 测试RouterOS文件
    echo ""
    log_info "测试RouterOS集成文件..."
    test_file_exists "routeros/ip-monitor.py" "IP监控脚本"
    test_file_exists "routeros/requirements.txt" "Python依赖文件"
    
    # 测试脚本语法
    echo ""
    log_info "测试脚本语法..."
    test_script_syntax "deploy.sh" "主部署脚本"
    test_script_syntax "install.sh" "快速安装脚本"
    test_script_syntax "scripts/backup.sh" "备份脚本"
    test_script_syntax "scripts/health-check.sh" "健康检查脚本"
    test_script_syntax "scripts/certificate-manager.sh" "证书管理脚本"
    
    # 测试Python脚本语法
    echo ""
    log_info "测试Python脚本语法..."
    test_python_syntax "routeros/ip-monitor.py" "IP监控脚本"
    
    # 测试YAML语法
    echo ""
    log_info "测试YAML语法..."
    test_yaml_syntax "docker-compose.yml" "Docker Compose配置"
    
    # 测试可执行权限
    echo ""
    log_info "测试可执行权限..."
    test_executable_permission "deploy.sh" "主部署脚本"
    test_executable_permission "install.sh" "快速安装脚本"
    test_executable_permission "scripts/backup.sh" "备份脚本"
    test_executable_permission "scripts/health-check.sh" "健康检查脚本"
    test_executable_permission "scripts/certificate-manager.sh" "证书管理脚本"
    test_executable_permission "routeros/ip-monitor.py" "IP监控脚本"
}

# 显示测试结果
show_results() {
    echo ""
    echo "=================================================="
    log_info "测试完成！"
    echo ""
    echo "测试统计:"
    echo "  总测试数: $TESTS_TOTAL"
    echo "  通过: $TESTS_PASSED"
    echo "  失败: $TESTS_FAILED"
    echo "  成功率: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_info "✓ 所有测试通过！项目文件完整。"
        echo ""
        echo "下一步操作:"
        echo "1. 编辑 config-example.env 并保存为 config.env"
        echo "2. 运行: sudo ./deploy.sh"
        echo "3. 或使用快速安装: sudo ./install.sh"
        return 0
    else
        log_error "✗ 有 $TESTS_FAILED 个测试失败，请检查项目文件。"
        return 1
    fi
}

# 显示项目信息
show_project_info() {
    echo ""
    log_info "Matrix Synapse 自动化部署项目"
    echo "=================================="
    echo "版本: 1.0.0"
    echo "日期: 2025-07-13"
    echo "适用: Debian 12, 内网环境"
    echo ""
    echo "主要功能:"
    echo "• 自动化部署 Matrix Synapse + Coturn"
    echo "• SSL证书自动管理 (acme.sh + Cloudflare)"
    echo "• RouterOS集成 (动态IP更新)"
    echo "• 自动备份和健康检查"
    echo "• 安全配置 (防火墙, fail2ban)"
    echo "• 详细的部署文档"
    echo ""
    echo "文件说明:"
    echo "• deploy.sh: 主要部署脚本"
    echo "• install.sh: 快速安装脚本"
    echo "• manual-guide.md: 详细手动部署指南"
    echo "• docker-compose.yml: Docker服务配置"
    echo "• configs/: 配置文件模板"
    echo "• scripts/: 管理脚本"
    echo "• routeros/: RouterOS集成"
    echo "=================================="
}

# 主函数
main() {
    case "${1:-}" in
        --info)
            show_project_info
            ;;
        --help|-h)
            echo "Matrix Synapse 项目测试脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --info      显示项目信息"
            echo "  --help      显示此帮助信息"
            echo ""
            echo "无参数运行将执行完整性测试"
            ;;
        "")
            show_project_info
            run_tests
            show_results
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
