# Matrix Synapse 手动部署指南

这是一份详细的Matrix Synapse手动部署指南，适用于内网环境，包含所有必要的配置步骤和高级安全设置。

## 目录

1. [系统要求](#系统要求)
2. [准备工作](#准备工作)
3. [基础环境安装](#基础环境安装)
4. [Docker环境配置](#docker环境配置)
5. [SSL证书配置](#ssl证书配置)
6. [Matrix服务部署](#matrix服务部署)
7. [网络配置](#网络配置)
8. [安全配置](#安全配置)
9. [RouterOS集成](#routeros集成)
10. [备份和监控](#备份和监控)
11. [故障排除](#故障排除)

## 系统要求

### 硬件要求
- **CPU**: 2核心或以上
- **内存**: 8GB RAM (推荐)
- **存储**: 50GB可用空间 (SSD推荐)
- **网络**: 稳定的网络连接，支持端口转发

### 软件要求
- **操作系统**: Debian 12 (Bookworm)
- **Docker**: 最新版本
- **域名**: 已配置DNS解析的域名
- **Cloudflare**: DNS托管和API访问

### 网络要求
- **内网环境**: 支持端口转发
- **路由器**: 支持RouterOS API (可选)
- **端口**: 8080/8443 (HTTP/HTTPS), 3478 (STUN/TURN), 65335-65535 (UDP媒体)

## 准备工作

### 1. 域名配置

确保您的域名已正确配置DNS解析：

```bash
# 检查DNS解析
nslookup your-domain.com
dig your-domain.com A
```

### 2. Cloudflare API Token

1. 登录Cloudflare控制台
2. 进入 "My Profile" > "API Tokens"
3. 创建自定义Token，权限设置：
   - Zone:Zone:Read
   - Zone:DNS:Edit
   - 包含所有区域

### 3. 路由器端口转发

配置路由器端口转发规则：

```
外部端口 -> 内部端口 -> 服务器IP
80       -> 8080      -> *************
443      -> 8443      -> *************
3478     -> 3478      -> *************
65335-65535 -> 65335-65535 -> ************* (UDP)
```

## 基础环境安装

### 1. 更新系统

```bash
# 更新包列表
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y \
    curl \
    wget \
    git \
    unzip \
    gnupg \
    lsb-release \
    ca-certificates \
    apt-transport-https \
    software-properties-common
```

### 2. 安装Python环境

```bash
# 安装Python和相关工具
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev

# 创建虚拟环境 (用于RouterOS脚本)
python3 -m venv /opt/matrix-venv
source /opt/matrix-venv/bin/activate
```

### 3. 安装系统工具

```bash
# 安装监控和管理工具
sudo apt install -y \
    htop \
    iotop \
    netstat-nat \
    sqlite3 \
    jq \
    bc \
    cron \
    logrotate
```

## Docker环境配置

### 1. 安装Docker

```bash
# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 验证安装
sudo docker --version
sudo docker compose version
```

### 2. 配置Docker

```bash
# 创建docker组并添加用户
sudo groupadd docker
sudo usermod -aG docker $USER

# 配置Docker日志轮转
sudo tee /etc/docker/daemon.json << EOF
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    }
}
EOF

# 重启Docker服务
sudo systemctl restart docker
```

## SSL证书配置

### 1. 安装acme.sh

```bash
# 下载并安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>

# 重新加载shell配置
source ~/.bashrc

# 设置Cloudflare API Token
export CF_Token="your-cloudflare-api-token"
```

### 2. 申请SSL证书

```bash
# 申请标准证书
~/.acme.sh/acme.sh --issue --dns dns_cf -d your-domain.com

# 或申请通配符证书 (推荐)
~/.acme.sh/acme.sh --issue --dns dns_cf -d your-domain.com -d "*.your-domain.com"
```

### 3. 安装证书

```bash
# 创建证书目录
sudo mkdir -p /opt/matrix/certs

# 安装证书
~/.acme.sh/acme.sh --install-cert -d your-domain.com \
    --cert-file /opt/matrix/certs/cert.pem \
    --key-file /opt/matrix/certs/key.pem \
    --fullchain-file /opt/matrix/certs/fullchain.pem \
    --ca-file /opt/matrix/certs/ca.pem

# 设置证书权限
sudo chmod 644 /opt/matrix/certs/*.pem
sudo chown root:root /opt/matrix/certs/*.pem
```

## Matrix服务部署

### 1. 创建目录结构

```bash
# 创建主目录
sudo mkdir -p /opt/matrix/{data,logs,configs,certs,backups}
sudo mkdir -p /opt/matrix/data/{media,uploads}
sudo mkdir -p /opt/matrix/logs/{nginx,coturn}

# 设置权限
sudo chown -R 991:991 /opt/matrix/data
sudo chmod -R 755 /opt/matrix
```

### 2. 配置文件准备

复制配置文件模板并根据您的环境进行修改：

```bash
# 复制配置文件模板
sudo cp configs/homeserver.yaml.template /opt/matrix/configs/homeserver.yaml
sudo cp configs/coturn.conf.template /opt/matrix/configs/coturn.conf
sudo cp configs/nginx.conf.template /opt/matrix/configs/nginx.conf
sudo cp configs/log.config.template /opt/matrix/configs/log.config

# 编辑配置文件，替换模板变量
sudo sed -i 's/{{DOMAIN_NAME}}/your-domain.com/g' /opt/matrix/configs/*.yaml
sudo sed -i 's/{{DOMAIN_NAME}}/your-domain.com/g' /opt/matrix/configs/*.conf
```

### 3. 生成密钥

```bash
# 生成Synapse签名密钥
sudo docker run --rm \
    -v /opt/matrix/data:/data \
    matrixdotorg/synapse:latest \
    generate

# 生成TURN共享密钥
TURN_SECRET=$(openssl rand -hex 32)
echo "TURN密钥: $TURN_SECRET"

# 更新配置文件中的密钥
sudo sed -i "s/{{TURN_SECRET}}/$TURN_SECRET/g" /opt/matrix/configs/homeserver.yaml
sudo sed -i "s/{{TURN_SECRET}}/$TURN_SECRET/g" /opt/matrix/configs/coturn.conf
```

### 4. 部署服务

```bash
# 复制docker-compose.yml
sudo cp docker-compose.yml /opt/matrix/

# 启动服务
cd /opt/matrix
sudo docker compose up -d

# 检查服务状态
sudo docker compose ps
sudo docker compose logs -f
```

## 网络配置

### 1. 防火墙配置 (可选)

```bash
# 安装UFW
sudo apt install -y ufw

# 配置防火墙规则
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许Matrix端口
sudo ufw allow 8080/tcp comment 'Matrix HTTP'
sudo ufw allow 8443/tcp comment 'Matrix HTTPS'
sudo ufw allow 3478 comment 'Coturn STUN/TURN'
sudo ufw allow 65335:65535/udp comment 'Coturn Media'

# 启用防火墙
sudo ufw --force enable

# 查看状态
sudo ufw status verbose
```

### 2. 网络优化

```bash
# 优化网络参数
sudo tee -a /etc/sysctl.conf << EOF
# Matrix网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq
EOF

# 应用配置
sudo sysctl -p
```

## 安全配置

### 1. fail2ban配置

```bash
# 安装fail2ban
sudo apt install -y fail2ban

# 创建配置文件
sudo tee /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s

[nginx-http-auth]
enabled = true
port = http,https
logpath = /opt/matrix/logs/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
port = http,https
logpath = /opt/matrix/logs/nginx/error.log
maxretry = 10
findtime = 600
bantime = 3600

[matrix-synapse]
enabled = true
port = 8008,8448
logpath = /opt/matrix/logs/homeserver.log
failregex = ^.*\[.*\] Failed login attempt from <HOST>.*$
maxretry = 5
EOF

# 启动fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# 查看状态
sudo fail2ban-client status
```

### 2. 系统安全加固

```bash
# 禁用不必要的服务
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon

# 配置SSH安全
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# 设置自动更新
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 3. 日志轮转配置

```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/matrix << EOF
/opt/matrix/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker compose -f /opt/matrix/docker-compose.yml restart nginx
    endscript
}

/opt/matrix/logs/nginx/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
    sharedscripts
    postrotate
        docker compose -f /opt/matrix/docker-compose.yml restart nginx
    endscript
}
EOF

# 测试配置
sudo logrotate -d /etc/logrotate.d/matrix
```

## RouterOS集成

### 1. 准备RouterOS环境

在RouterOS设备上创建API用户：

```routeros
# 创建API用户组
/user group add name=api-users policy=api,read

# 创建API用户
/user add name=matrix-api group=api-users password=your-secure-password

# 启用API服务
/ip service enable api
/ip service set api port=8728
```

### 2. 安装Python依赖

```bash
# 激活虚拟环境
source /opt/matrix-venv/bin/activate

# 安装依赖
cd /opt/matrix/routeros
pip install -r requirements.txt
```

### 3. 配置IP监控

```bash
# 创建配置文件
sudo tee /opt/matrix/configs/ip-monitor.yaml << EOF
routeros:
  host: "***********"
  username: "matrix-api"
  password: "your-secure-password"
  port: 8728
  wan_interface: "WAN"

coturn:
  config_path: "/opt/matrix/configs/coturn.conf"
  docker_service: "matrix-coturn"
  compose_path: "/opt/matrix/docker-compose.yml"

monitoring:
  check_interval: 60
  log_level: "INFO"
  log_file: "/opt/matrix/logs/ip-monitor.log"
  state_file: "/opt/matrix/data/ip-state.json"
EOF

# 设置权限
sudo chmod 600 /opt/matrix/configs/ip-monitor.yaml
```

### 4. 设置定时任务

```bash
# 添加到crontab
sudo crontab -e

# 添加以下行
# 每分钟检查IP变化
* * * * * /opt/matrix-venv/bin/python /opt/matrix/routeros/ip-monitor.py -c /opt/matrix/configs/ip-monitor.yaml

# 每天凌晨2点备份
0 2 * * * /opt/matrix/scripts/backup.sh

# 每小时健康检查
0 * * * * /opt/matrix/scripts/health-check.sh

# 每天检查证书续期
0 3 * * * /opt/matrix/scripts/certificate-manager.sh auto-renew
```

## 备份和监控

### 1. 配置自动备份

```bash
# 设置备份脚本权限
sudo chmod +x /opt/matrix/scripts/backup.sh

# 测试备份
sudo /opt/matrix/scripts/backup.sh

# 配置远程备份 (可选)
export REMOTE_BACKUP_ENABLED=true
export REMOTE_BACKUP_HOST=backup.example.com
export REMOTE_BACKUP_USER=backup
export REMOTE_BACKUP_PATH=/backups/matrix
```

### 2. 配置健康检查

```bash
# 设置健康检查脚本权限
sudo chmod +x /opt/matrix/scripts/health-check.sh

# 测试健康检查
sudo /opt/matrix/scripts/health-check.sh

# 配置通知 (可选)
export NOTIFICATION_ENABLED=true
export WEBHOOK_URL=https://hooks.slack.com/your-webhook-url
export EMAIL_TO=<EMAIL>
```

### 3. 监控仪表板 (可选)

安装Grafana和Prometheus进行监控：

```bash
# 创建监控目录
sudo mkdir -p /opt/matrix/monitoring

# 下载Prometheus配置
sudo tee /opt/matrix/monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'matrix-synapse'
    static_configs:
      - targets: ['localhost:9000']
  
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
EOF

# 添加监控服务到docker-compose.yml
# (参考项目中的监控配置示例)
```

## 故障排除

### 1. 常见问题

#### 服务无法启动
```bash
# 检查Docker日志
sudo docker compose logs synapse
sudo docker compose logs coturn
sudo docker compose logs nginx

# 检查配置文件语法
sudo docker run --rm -v /opt/matrix/configs:/configs \
    matrixdotorg/synapse:latest \
    python -m synapse.config.homeserver -c /configs/homeserver.yaml
```

#### 端口无法访问
```bash
# 检查端口监听
sudo netstat -tuln | grep -E ':(8008|8443|3478)'

# 检查防火墙
sudo ufw status
sudo iptables -L

# 检查路由器端口转发
# 使用在线端口检查工具验证
```

#### SSL证书问题
```bash
# 检查证书有效期
openssl x509 -in /opt/matrix/certs/fullchain.pem -noout -dates

# 测试SSL连接
openssl s_client -connect your-domain.com:8443 -servername your-domain.com

# 重新申请证书
/opt/matrix/scripts/certificate-manager.sh issue
```

### 2. 日志分析

```bash
# 查看Synapse日志
sudo tail -f /opt/matrix/logs/homeserver.log

# 查看Nginx日志
sudo tail -f /opt/matrix/logs/nginx/error.log
sudo tail -f /opt/matrix/logs/nginx/access.log

# 查看Coturn日志
sudo docker compose logs -f coturn

# 搜索错误
sudo grep -i error /opt/matrix/logs/*.log
```

### 3. 性能优化

```bash
# 检查系统资源
htop
iotop
df -h

# 优化SQLite数据库
sudo sqlite3 /opt/matrix/data/homeserver.db "VACUUM;"
sudo sqlite3 /opt/matrix/data/homeserver.db "REINDEX;"

# 清理旧媒体文件
sudo docker exec matrix-synapse \
    python -m synapse.app.admin_cmd \
    -c /data/homeserver.yaml \
    purge_remote_media \
    --before="30 days ago"
```

### 4. 数据恢复

```bash
# 从备份恢复
sudo /opt/matrix/scripts/backup.sh --restore /path/to/backup.tar.gz

# 手动恢复数据库
sudo docker compose down
sudo cp /path/to/backup/homeserver.db /opt/matrix/data/
sudo chown 991:991 /opt/matrix/data/homeserver.db
sudo docker compose up -d
```

## 维护建议

### 1. 定期维护任务

- **每日**: 检查服务状态和日志
- **每周**: 检查磁盘空间和备份
- **每月**: 更新系统和Docker镜像
- **每季度**: 检查安全配置和证书

### 2. 监控指标

- 服务可用性
- 响应时间
- 磁盘使用率
- 内存使用率
- 证书有效期
- 备份状态

### 3. 安全检查

- 定期更新系统
- 检查fail2ban日志
- 监控异常登录
- 审查用户权限
- 检查网络配置

---

这份指南涵盖了Matrix Synapse在内网环境下的完整部署流程。如果遇到问题，请参考故障排除部分或查看项目文档。
