# Matrix Synapse 手动部署指南

这是一份详细的Matrix Synapse手动部署指南，适用于内网环境，包含所有必要的配置步骤和高级安全设置。

## 目录

1. [系统要求](#系统要求)
2. [准备工作](#准备工作)
3. [基础环境安装](#基础环境安装)
4. [Docker环境配置](#docker环境配置)
5. [SSL证书配置](#ssl证书配置)
6. [Matrix服务部署](#matrix服务部署)
7. [网络配置](#网络配置)
8. [安全配置](#安全配置)
9. [RouterOS集成](#routeros集成)
10. [备份和监控](#备份和监控)
11. [故障排除](#故障排除)

## 系统要求

### 硬件要求
- **CPU**: 2核心或以上
- **内存**: 8GB RAM (推荐)
- **存储**: 50GB可用空间 (SSD推荐)
- **网络**: 稳定的网络连接，支持端口转发

### 软件要求
- **操作系统**: Debian 12 (Bookworm)
- **Docker**: 最新版本
- **域名**: 已配置DNS解析的域名
- **Cloudflare**: DNS托管和API访问

### 网络要求
- **内网环境**: 支持端口转发
- **路由器**: 支持RouterOS API (可选)
- **端口**: 8080/8443 (HTTP/HTTPS), 3478 (STUN/TURN), 65335-65535 (UDP媒体)

## 准备工作

### 1. 域名配置

#### 子域名结构说明

本项目使用子域名架构，提供更好的服务分离和SSL证书管理：

- **基础域名**: `example.com` (用于重定向到Matrix服务)
- **Matrix子域名**: `matrix.example.com` (Matrix Synapse主服务)
- **TURN子域名**: `turn.example.com` (TURN/STUN服务，无Web界面)

#### DNS配置要求

在Cloudflare中配置以下DNS记录：

```bash
# A记录配置 (指向您的公网IP)
A    example.com        -> 你的公网IP
A    matrix.example.com -> 你的公网IP
A    turn.example.com   -> 你的公网IP

# 检查DNS解析
nslookup example.com
nslookup matrix.example.com
nslookup turn.example.com
```

### 2. Cloudflare API Token

1. 登录Cloudflare控制台
2. 进入 "My Profile" > "API Tokens"
3. 创建自定义Token，权限设置：
   - Zone:Zone:Read
   - Zone:DNS:Edit
   - 包含所有区域

**重要**: 由于需要为多个子域名申请独立证书，确保API Token有足够权限。

### 3. 路由器端口转发

配置路由器端口转发规则（ISP封禁80/443端口的解决方案）：

```
外部端口 -> 内部端口 -> 服务器IP -> 说明
8080     -> 8080      -> ***********00 -> HTTP (重定向)
8443     -> 8443      -> ***********00 -> HTTPS (主要访问)
3478     -> 3478      -> ***********00 -> STUN/TURN (TCP/UDP)
5349     -> 5349      -> ***********00 -> TURNS (TCP)
65335-65535 -> 65335-65535 -> ***********00 -> UDP媒体 (UDP)
```

## 基础环境安装

### 1. 更新系统

```bash
# 更新包列表
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y \
    curl \
    wget \
    git \
    unzip \
    gnupg \
    lsb-release \
    ca-certificates \
    apt-transport-https \
    software-properties-common
```

### 2. 安装Python环境

```bash
# 安装Python和相关工具
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev

# 创建虚拟环境 (用于RouterOS脚本)
python3 -m venv /opt/matrix-venv
source /opt/matrix-venv/bin/activate
```

### 3. 安装系统工具

```bash
# 安装监控和管理工具
sudo apt install -y \
    htop \
    iotop \
    netstat-nat \
    sqlite3 \
    jq \
    bc \
    cron \
    logrotate
```

## Docker环境配置

### 1. 安装Docker

```bash
# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 验证安装
sudo docker --version
sudo docker compose version
```

### 2. 配置Docker

```bash
# 创建docker组并添加用户
sudo groupadd docker
sudo usermod -aG docker $USER

# 配置Docker日志轮转
sudo tee /etc/docker/daemon.json << EOF
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    }
}
EOF

# 重启Docker服务
sudo systemctl restart docker
```

## SSL证书配置

### 1. 安装acme.sh

```bash
# 下载并安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>

# 重新加载shell配置
source ~/.bashrc

# 设置Cloudflare API Token
export CF_Token="your-cloudflare-api-token"
```

### 2. 申请独立子域名证书

由于采用子域名架构，需要为每个子域名申请独立的SSL证书：

```bash
# 创建证书目录结构
sudo mkdir -p /opt/matrix/certs/{matrix,turn,base}

# 申请Matrix子域名证书
~/.acme.sh/acme.sh --issue --dns dns_cf -d matrix.your-domain.com

# 申请TURN子域名证书
~/.acme.sh/acme.sh --issue --dns dns_cf -d turn.your-domain.com

# 申请基础域名证书
~/.acme.sh/acme.sh --issue --dns dns_cf -d your-domain.com
```

### 3. 安装证书到指定目录

```bash
# 安装Matrix证书
~/.acme.sh/acme.sh --install-cert -d matrix.your-domain.com \
    --cert-file /opt/matrix/certs/matrix/cert.pem \
    --key-file /opt/matrix/certs/matrix/key.pem \
    --fullchain-file /opt/matrix/certs/matrix/fullchain.pem \
    --ca-file /opt/matrix/certs/matrix/ca.pem

# 安装TURN证书
~/.acme.sh/acme.sh --install-cert -d turn.your-domain.com \
    --cert-file /opt/matrix/certs/turn/cert.pem \
    --key-file /opt/matrix/certs/turn/key.pem \
    --fullchain-file /opt/matrix/certs/turn/fullchain.pem \
    --ca-file /opt/matrix/certs/turn/ca.pem

# 安装基础域名证书
~/.acme.sh/acme.sh --install-cert -d your-domain.com \
    --cert-file /opt/matrix/certs/base/cert.pem \
    --key-file /opt/matrix/certs/base/key.pem \
    --fullchain-file /opt/matrix/certs/base/fullchain.pem \
    --ca-file /opt/matrix/certs/base/ca.pem

# 设置证书权限
sudo chmod -R 644 /opt/matrix/certs/
sudo find /opt/matrix/certs/ -type d -exec chmod 755 {} \;
sudo chown -R root:root /opt/matrix/certs/
```

### 4. 证书目录结构

安装完成后的证书目录结构：

```
/opt/matrix/certs/
├── matrix/                 # Matrix子域名证书
│   ├── cert.pem
│   ├── key.pem
│   ├── fullchain.pem
│   └── ca.pem
├── turn/                   # TURN子域名证书
│   ├── cert.pem
│   ├── key.pem
│   ├── fullchain.pem
│   └── ca.pem
└── base/                   # 基础域名证书
    ├── cert.pem
    ├── key.pem
    ├── fullchain.pem
    └── ca.pem
```

## Matrix服务部署

### 1. 创建目录结构

```bash
# 创建主目录
sudo mkdir -p /opt/matrix/{data,logs,configs,certs,backups}
sudo mkdir -p /opt/matrix/data/{media,uploads}
sudo mkdir -p /opt/matrix/logs/{nginx,coturn}

# 设置权限
sudo chown -R 991:991 /opt/matrix/data
sudo chmod -R 755 /opt/matrix
```

### 2. 配置文件准备

复制配置文件模板并根据您的环境进行修改：

```bash
# 复制配置文件模板
sudo cp internal/configs/homeserver.yaml.template /opt/matrix/configs/homeserver.yaml
sudo cp internal/configs/coturn.conf.template /opt/matrix/configs/coturn.conf
sudo cp internal/configs/nginx.conf.template /opt/matrix/configs/nginx.conf
sudo cp internal/configs/log.config.template /opt/matrix/configs/log.config

# 设置域名变量
BASE_DOMAIN="your-domain.com"
MATRIX_DOMAIN="matrix.your-domain.com"
TURN_DOMAIN="turn.your-domain.com"
HTTPS_PORT="8443"
HTTP_PORT="8080"

# 编辑Synapse配置文件，替换模板变量
sudo sed -i "s/{{DOMAIN_NAME}}/$MATRIX_DOMAIN/g" /opt/matrix/configs/homeserver.yaml
sudo sed -i "s/{{TURN_DOMAIN}}/$TURN_DOMAIN/g" /opt/matrix/configs/homeserver.yaml

# 编辑Coturn配置文件
sudo sed -i "s/{{DOMAIN_NAME}}/$TURN_DOMAIN/g" /opt/matrix/configs/coturn.conf

# 编辑Nginx配置文件
sudo sed -i "s/{{BASE_DOMAIN}}/$BASE_DOMAIN/g" /opt/matrix/configs/nginx.conf
sudo sed -i "s/{{MATRIX_DOMAIN}}/$MATRIX_DOMAIN/g" /opt/matrix/configs/nginx.conf
sudo sed -i "s/{{TURN_DOMAIN}}/$TURN_DOMAIN/g" /opt/matrix/configs/nginx.conf
sudo sed -i "s/{{HTTPS_PORT}}/$HTTPS_PORT/g" /opt/matrix/configs/nginx.conf
sudo sed -i "s/{{HTTP_PORT}}/$HTTP_PORT/g" /opt/matrix/configs/nginx.conf
```

### 3. 生成密钥和完成配置

```bash
# 生成所有必需的密钥
MACAROON_SECRET=$(openssl rand -hex 32)
REGISTRATION_SECRET=$(openssl rand -hex 32)
FORM_SECRET=$(openssl rand -hex 32)
TURN_SECRET=$(openssl rand -hex 32)

echo "生成的密钥:"
echo "MACAROON_SECRET: $MACAROON_SECRET"
echo "REGISTRATION_SECRET: $REGISTRATION_SECRET"
echo "FORM_SECRET: $FORM_SECRET"
echo "TURN_SECRET: $TURN_SECRET"

# 更新Synapse配置文件中的密钥
sudo sed -i "s/{{MACAROON_SECRET}}/$MACAROON_SECRET/g" /opt/matrix/configs/homeserver.yaml
sudo sed -i "s/{{REGISTRATION_SECRET}}/$REGISTRATION_SECRET/g" /opt/matrix/configs/homeserver.yaml
sudo sed -i "s/{{FORM_SECRET}}/$FORM_SECRET/g" /opt/matrix/configs/homeserver.yaml
sudo sed -i "s/{{TURN_SECRET}}/$TURN_SECRET/g" /opt/matrix/configs/homeserver.yaml

# 更新Coturn配置文件中的密钥
sudo sed -i "s/{{TURN_SECRET}}/$TURN_SECRET/g" /opt/matrix/configs/coturn.conf

# 设置其他配置选项
sudo sed -i "s/{{ENABLE_REGISTRATION}}/false/g" /opt/matrix/configs/homeserver.yaml
sudo sed -i "s/{{FEDERATION_CONFIG}}//g" /opt/matrix/configs/homeserver.yaml  # 启用联邦
sudo sed -i "s/{{COTURN_PORT_START}}/65335/g" /opt/matrix/configs/coturn.conf
sudo sed -i "s/{{COTURN_PORT_END}}/65535/g" /opt/matrix/configs/coturn.conf
sudo sed -i "s/{{EXTERNAL_IP}}/127.0.0.1/g" /opt/matrix/configs/coturn.conf

# 生成Synapse签名密钥
sudo docker run --rm \
    -v /opt/matrix/data:/data \
    -v /opt/matrix/configs/homeserver.yaml:/data/homeserver.yaml:ro \
    ghcr.io/element-hq/synapse:latest \
    generate
```

### 4. 部署服务

```bash
# 复制docker-compose.yml
sudo cp internal/docker-compose.yml /opt/matrix/

# 设置环境变量 (Docker Compose需要)
export MATRIX_DOMAIN="matrix.your-domain.com"

# 启动服务
cd /opt/matrix
sudo -E docker compose up -d

# 检查服务状态
sudo docker compose ps
sudo docker compose logs -f

# 验证服务运行
sudo docker compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
```

## 网络配置

### 1. 防火墙配置 (可选)

```bash
# 安装UFW
sudo apt install -y ufw

# 配置防火墙规则
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许Matrix端口
sudo ufw allow 8080/tcp comment 'Matrix HTTP'
sudo ufw allow 8443/tcp comment 'Matrix HTTPS'
sudo ufw allow 3478 comment 'Coturn STUN/TURN'
sudo ufw allow 65335:65535/udp comment 'Coturn Media'

# 启用防火墙
sudo ufw --force enable

# 查看状态
sudo ufw status verbose
```

### 2. 网络优化

```bash
# 优化网络参数
sudo tee -a /etc/sysctl.conf << EOF
# Matrix网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq
EOF

# 应用配置
sudo sysctl -p
```

### 3. 服务访问验证

验证各个子域名服务是否正常工作：

```bash
# 检查Matrix服务
curl -I https://matrix.your-domain.com:8443/_matrix/client/versions

# 检查基础域名重定向
curl -I https://your-domain.com:8443

# 检查TURN服务端口
netstat -tuln | grep -E ':(3478|5349)'

# 测试TURN连接 (需要安装coturn-utils)
# sudo apt install coturn-utils
# turnutils_uclient -t turn.your-domain.com -p 3478
```

## 安全配置

### 1. fail2ban配置

```bash
# 安装fail2ban
sudo apt install -y fail2ban

# 创建配置文件
sudo tee /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s

[nginx-http-auth]
enabled = true
port = http,https
logpath = /opt/matrix/logs/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
port = http,https
logpath = /opt/matrix/logs/nginx/error.log
maxretry = 10
findtime = 600
bantime = 3600

[matrix-synapse]
enabled = true
port = 8008,8448
logpath = /opt/matrix/logs/homeserver.log
failregex = ^.*\[.*\] Failed login attempt from <HOST>.*$
maxretry = 5
EOF

# 启动fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# 查看状态
sudo fail2ban-client status
```

### 2. 系统安全加固

```bash
# 禁用不必要的服务
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon

# 配置SSH安全
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# 设置自动更新
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 4. 日志轮转配置

```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/matrix << EOF
/opt/matrix/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker compose -f /opt/matrix/docker-compose.yml restart nginx
    endscript
}

/opt/matrix/logs/nginx/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
    sharedscripts
    postrotate
        docker compose -f /opt/matrix/docker-compose.yml restart nginx
    endscript
}
EOF

# 测试配置
sudo logrotate -d /etc/logrotate.d/matrix
```

## RouterOS集成

### 1. 准备RouterOS环境

在RouterOS设备上创建API用户：

```routeros
# 创建API用户组
/user group add name=api-users policy=api,read

# 创建API用户
/user add name=matrix-api group=api-users password=your-secure-password

# 启用API服务
/ip service enable api
/ip service set api port=8728
```

### 2. 安装Python依赖

```bash
# 激活虚拟环境
source /opt/matrix-venv/bin/activate

# 安装依赖
cd /opt/matrix/routeros
pip install -r requirements.txt
```

### 3. 配置IP监控

```bash
# 创建配置文件
sudo tee /opt/matrix/configs/ip-monitor.yaml << EOF
routeros:
  host: "***********"
  username: "matrix-api"
  password: "your-secure-password"
  port: 8728
  wan_interface: "WAN"

coturn:
  config_path: "/opt/matrix/configs/coturn.conf"
  docker_service: "matrix-coturn"
  compose_path: "/opt/matrix/docker-compose.yml"

monitoring:
  check_interval: 60
  log_level: "INFO"
  log_file: "/opt/matrix/logs/ip-monitor.log"
  state_file: "/opt/matrix/data/ip-state.json"
EOF

# 设置权限
sudo chmod 600 /opt/matrix/configs/ip-monitor.yaml

# 复制监控脚本
sudo cp internal/routeros/ip-monitor.py /opt/matrix/scripts/
sudo chmod +x /opt/matrix/scripts/ip-monitor.py
```

### 4. 设置定时任务

```bash
# 添加到crontab
sudo crontab -e

# 添加以下行
# 每分钟检查IP变化
* * * * * /opt/matrix-venv/bin/python /opt/matrix/routeros/ip-monitor.py -c /opt/matrix/configs/ip-monitor.yaml

# 每天凌晨2点备份
0 2 * * * /opt/matrix/scripts/backup.sh

# 每小时健康检查
0 * * * * /opt/matrix/scripts/health-check.sh

# 每天检查证书续期
0 3 * * * /opt/matrix/scripts/certificate-manager.sh auto-renew
```

## 备份和监控

### 1. 配置自动备份

```bash
# 复制备份脚本
sudo cp internal/scripts/backup.sh /opt/matrix/scripts/
sudo chmod +x /opt/matrix/scripts/backup.sh

# 测试备份
sudo INSTALL_PATH=/opt/matrix /opt/matrix/scripts/backup.sh

# 配置远程备份 (可选)
export REMOTE_BACKUP_ENABLED=true
export REMOTE_BACKUP_HOST=backup.example.com
export REMOTE_BACKUP_USER=backup
export REMOTE_BACKUP_PATH=/backups/matrix

# 运行带远程备份的测试
sudo INSTALL_PATH=/opt/matrix \
     REMOTE_BACKUP_ENABLED=true \
     REMOTE_BACKUP_HOST=backup.example.com \
     REMOTE_BACKUP_USER=backup \
     REMOTE_BACKUP_PATH=/backups/matrix \
     /opt/matrix/scripts/backup.sh
```

### 2. 配置健康检查

```bash
# 复制健康检查脚本
sudo cp internal/scripts/health-check.sh /opt/matrix/scripts/
sudo chmod +x /opt/matrix/scripts/health-check.sh

# 测试健康检查
sudo INSTALL_PATH=/opt/matrix /opt/matrix/scripts/health-check.sh

# 配置通知 (可选)
sudo INSTALL_PATH=/opt/matrix \
     NOTIFICATION_ENABLED=true \
     WEBHOOK_URL=https://hooks.slack.com/your-webhook-url \
     EMAIL_TO=<EMAIL> \
     /opt/matrix/scripts/health-check.sh
```

### 3. 监控仪表板 (可选)

安装Grafana和Prometheus进行监控：

```bash
# 创建监控目录
sudo mkdir -p /opt/matrix/monitoring

# 下载Prometheus配置
sudo tee /opt/matrix/monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'matrix-synapse'
    static_configs:
      - targets: ['localhost:9000']
  
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
EOF

# 添加监控服务到docker-compose.yml
# (参考项目中的监控配置示例)
```

## 故障排除

### 1. 常见问题

#### 服务无法启动
```bash
# 检查Docker日志
cd /opt/matrix
sudo docker compose logs synapse
sudo docker compose logs coturn
sudo docker compose logs nginx

# 检查配置文件语法
sudo docker run --rm -v /opt/matrix/configs:/configs \
    ghcr.io/element-hq/synapse:latest \
    python -m synapse.config.homeserver -c /configs/homeserver.yaml

# 检查环境变量
echo "MATRIX_DOMAIN: $MATRIX_DOMAIN"
```

#### 端口无法访问
```bash
# 检查端口监听
sudo netstat -tuln | grep -E ':(8008|8080|8443|3478|5349)'

# 检查防火墙
sudo ufw status
sudo iptables -L

# 检查路由器端口转发
# 使用在线端口检查工具验证外部访问

# 测试内部连接
curl -I http://localhost:8080
curl -I https://localhost:8443 -k
```

#### SSL证书问题
```bash
# 检查各个子域名证书有效期
openssl x509 -in /opt/matrix/certs/matrix/fullchain.pem -noout -dates
openssl x509 -in /opt/matrix/certs/turn/fullchain.pem -noout -dates
openssl x509 -in /opt/matrix/certs/base/fullchain.pem -noout -dates

# 测试SSL连接
openssl s_client -connect matrix.your-domain.com:8443 -servername matrix.your-domain.com
openssl s_client -connect your-domain.com:8443 -servername your-domain.com

# 重新申请证书
sudo cp internal/scripts/certificate-manager.sh /opt/matrix/scripts/
sudo chmod +x /opt/matrix/scripts/certificate-manager.sh
sudo DOMAIN_NAME=matrix.your-domain.com \
     CF_TOKEN=your-token \
     INSTALL_PATH=/opt/matrix \
     /opt/matrix/scripts/certificate-manager.sh issue
```

#### TURN服务连接问题
```bash
# 检查TURN服务状态
sudo docker compose logs coturn

# 检查TURN端口
sudo netstat -tuln | grep -E ':(3478|5349)'

# 测试TURN连接 (需要安装coturn-utils)
sudo apt install coturn-utils
turnutils_uclient -t turn.your-domain.com -p 3478

# 检查Coturn配置
sudo docker exec matrix-coturn cat /etc/coturn/turnserver.conf
```

### 2. 日志分析

```bash
# 查看Synapse日志
sudo tail -f /opt/matrix/logs/homeserver.log

# 查看Nginx日志
sudo tail -f /opt/matrix/logs/nginx/error.log
sudo tail -f /opt/matrix/logs/nginx/access.log

# 查看Coturn日志
cd /opt/matrix
sudo docker compose logs -f coturn

# 查看所有服务日志
sudo docker compose logs -f

# 搜索错误
sudo grep -i error /opt/matrix/logs/*.log
sudo grep -i "failed\|error\|critical" /opt/matrix/logs/homeserver.log
```

### 3. 性能优化

```bash
# 检查系统资源
htop
iotop
df -h

# 优化SQLite数据库
sudo sqlite3 /opt/matrix/data/homeserver.db "VACUUM;"
sudo sqlite3 /opt/matrix/data/homeserver.db "REINDEX;"

# 清理旧媒体文件
sudo docker exec matrix-synapse \
    python -m synapse.app.admin_cmd \
    -c /data/homeserver.yaml \
    purge_remote_media \
    --before="30 days ago"
```

### 4. 数据恢复

```bash
# 从备份恢复 (如果备份脚本支持恢复功能)
sudo INSTALL_PATH=/opt/matrix /opt/matrix/scripts/backup.sh --restore /path/to/backup.tar.gz

# 手动恢复数据库
cd /opt/matrix
sudo docker compose down
sudo cp /path/to/backup/homeserver.db /opt/matrix/data/
sudo chown 991:991 /opt/matrix/data/homeserver.db
sudo -E docker compose up -d

# 恢复证书
sudo cp -r /path/to/backup/certs/* /opt/matrix/certs/
sudo chmod -R 644 /opt/matrix/certs/
sudo find /opt/matrix/certs/ -type d -exec chmod 755 {} \;
```

### 5. 客户端配置验证

```bash
# 验证Matrix服务发现
curl https://matrix.your-domain.com:8443/.well-known/matrix/client
curl https://your-domain.com:8443/.well-known/matrix/client

# 验证服务器信息
curl https://matrix.your-domain.com:8443/_matrix/client/versions

# 检查联邦连接 (如果启用)
curl https://matrix.your-domain.com:8443/_matrix/federation/v1/version
```

## 维护建议

### 1. 定期维护任务

- **每日**: 检查服务状态和日志
- **每周**: 检查磁盘空间和备份
- **每月**: 更新系统和Docker镜像
- **每季度**: 检查安全配置和证书

### 2. 监控指标

- 服务可用性 (Matrix、TURN服务)
- 响应时间 (各子域名访问)
- 磁盘使用率
- 内存使用率
- 证书有效期 (所有子域名)
- 备份状态

### 3. 安全检查

- 定期更新系统
- 检查fail2ban日志
- 监控异常登录
- 审查用户权限
- 检查网络配置
- 验证TURN服务安全性

### 4. 证书管理

```bash
# 检查所有证书状态
sudo /opt/matrix/scripts/certificate-manager.sh status

# 手动续期所有证书
sudo DOMAIN_NAME=matrix.your-domain.com \
     CF_TOKEN=your-token \
     INSTALL_PATH=/opt/matrix \
     /opt/matrix/scripts/certificate-manager.sh auto-renew
```

### 5. 服务访问地址

部署完成后的服务访问地址：

- **Matrix客户端**: `https://matrix.your-domain.com:8443`
- **管理界面**: `https://matrix.your-domain.com:8443/_synapse/admin`
- **基础域名**: `https://your-domain.com:8443` (重定向到Matrix)
- **TURN服务**: `turn://turn.your-domain.com:3478` (无Web界面)

### 6. 客户端配置

**Element客户端配置**:
- 服务器: `matrix.your-domain.com:8443`
- 用户名格式: `@username:matrix.your-domain.com`

**其他客户端**:
- 大多数Matrix客户端会自动发现服务器配置
- 如需手动配置，使用Matrix子域名和端口8443

---

这份指南涵盖了Matrix Synapse在内网环境下使用子域名架构的完整部署流程。所有配置都已针对ISP封禁80/443端口的情况进行优化，并支持独立的子域名SSL证书管理。如果遇到问题，请参考故障排除部分或查看项目文档。
