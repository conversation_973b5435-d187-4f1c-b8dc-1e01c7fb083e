#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
RouterOS IP监控脚本
自动获取RouterOS WAN口公网IP并更新Coturn配置
适用于内网Matrix Synapse部署

功能:
- 连接RouterOS API获取WAN接口IP
- 检测IP变化并更新Coturn配置
- 重启Coturn服务应用新配置
- 支持Debian 12虚拟环境
- 详细日志记录和错误处理

作者: Matrix Deploy Project
版本: 1.0.0
日期: 2025-07-13
"""

import os
import sys
import time
import json
import logging
import subprocess
import signal
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# 第三方库导入
try:
    import routeros_api
    import yaml
    from tenacity import retry, stop_after_attempt, wait_exponential
    import colorlog
except ImportError as e:
    print(f"错误: 缺少必要的Python包: {e}")
    print("请运行: pip install -r requirements.txt")
    sys.exit(1)

# 配置常量
DEFAULT_CONFIG = {
    'routeros': {
        'host': '***********',
        'username': 'api',
        'password': 'api',
        'port': 8728,
        'wan_interface': 'WAN'
    },
    'coturn': {
        'config_path': '/opt/matrix/configs/coturn.conf',
        'docker_service': 'matrix-coturn',
        'compose_path': '/opt/matrix/docker-compose.yml'
    },
    'monitoring': {
        'check_interval': 60,  # 秒
        'log_level': 'INFO',
        'log_file': '/opt/matrix/logs/ip-monitor.log',
        'state_file': '/opt/matrix/data/ip-state.json'
    }
}

class ColoredFormatter(colorlog.ColoredFormatter):
    """自定义彩色日志格式化器"""
    
    def __init__(self):
        super().__init__(
            "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )

class IPMonitor:
    """RouterOS IP监控器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化监控器"""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        self.running = True
        self.current_ip = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("IP监控器初始化完成")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置文件"""
        config = DEFAULT_CONFIG.copy()
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f)
                    config.update(user_config)
            except Exception as e:
                print(f"警告: 无法加载配置文件 {config_path}: {e}")
                print("使用默认配置")
        
        return config
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('IPMonitor')
        logger.setLevel(getattr(logging, self.config['monitoring']['log_level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(ColoredFormatter())
        logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = self.config['monitoring']['log_file']
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止监控...")
        self.running = False
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _connect_routeros(self) -> routeros_api.RouterOsApiPool:
        """连接RouterOS API"""
        try:
            connection = routeros_api.RouterOsApiPool(
                host=self.config['routeros']['host'],
                username=self.config['routeros']['username'],
                password=self.config['routeros']['password'],
                port=self.config['routeros']['port'],
                plaintext_login=True
            )
            self.logger.debug("RouterOS API连接成功")
            return connection
        except Exception as e:
            self.logger.error(f"RouterOS API连接失败: {e}")
            raise
    
    def get_wan_ip(self) -> Optional[str]:
        """获取WAN接口IP地址"""
        try:
            connection = self._connect_routeros()
            api = connection.get_api()
            
            # 获取接口信息
            interfaces = api.get_resource('/interface').get()
            wan_interface = None
            
            for interface in interfaces:
                if interface.get('name') == self.config['routeros']['wan_interface']:
                    wan_interface = interface
                    break
            
            if not wan_interface:
                self.logger.error(f"未找到WAN接口: {self.config['routeros']['wan_interface']}")
                return None
            
            # 获取IP地址
            addresses = api.get_resource('/ip/address').get()
            for address in addresses:
                if address.get('interface') == self.config['routeros']['wan_interface']:
                    ip_with_mask = address.get('address', '')
                    if '/' in ip_with_mask:
                        ip = ip_with_mask.split('/')[0]
                        self.logger.debug(f"获取到WAN IP: {ip}")
                        return ip
            
            self.logger.warning(f"WAN接口 {self.config['routeros']['wan_interface']} 没有IP地址")
            return None
            
        except Exception as e:
            self.logger.error(f"获取WAN IP失败: {e}")
            return None
        finally:
            try:
                connection.disconnect()
            except:
                pass
    
    def _load_state(self) -> Dict[str, Any]:
        """加载状态文件"""
        state_file = self.config['monitoring']['state_file']
        
        if os.path.exists(state_file):
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"无法加载状态文件: {e}")
        
        return {}
    
    def _save_state(self, state: Dict[str, Any]):
        """保存状态文件"""
        state_file = self.config['monitoring']['state_file']
        os.makedirs(os.path.dirname(state_file), exist_ok=True)
        
        try:
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"无法保存状态文件: {e}")
    
    def update_coturn_config(self, new_ip: str) -> bool:
        """更新Coturn配置文件"""
        config_path = self.config['coturn']['config_path']
        
        if not os.path.exists(config_path):
            self.logger.error(f"Coturn配置文件不存在: {config_path}")
            return False
        
        try:
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找并替换external-ip行
            lines = content.split('\n')
            updated = False
            
            for i, line in enumerate(lines):
                if line.strip().startswith('external-ip='):
                    old_ip = line.split('=')[1] if '=' in line else 'unknown'
                    lines[i] = f'external-ip={new_ip}'
                    updated = True
                    self.logger.info(f"更新external-ip: {old_ip} -> {new_ip}")
                    break
            
            if not updated:
                # 如果没有找到external-ip行，添加一行
                lines.append(f'external-ip={new_ip}')
                self.logger.info(f"添加external-ip: {new_ip}")
            
            # 写回配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新Coturn配置失败: {e}")
            return False
    
    def restart_coturn(self) -> bool:
        """重启Coturn服务"""
        try:
            compose_path = self.config['coturn']['compose_path']
            service_name = self.config['coturn']['docker_service']
            
            # 重启Docker服务
            cmd = f"docker-compose -f {compose_path} restart {service_name}"
            result = subprocess.run(
                cmd, shell=True, capture_output=True, text=True, timeout=60
            )
            
            if result.returncode == 0:
                self.logger.info("Coturn服务重启成功")
                return True
            else:
                self.logger.error(f"Coturn服务重启失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("Coturn服务重启超时")
            return False
        except Exception as e:
            self.logger.error(f"重启Coturn服务时发生错误: {e}")
            return False
    
    def run(self):
        """运行监控循环"""
        self.logger.info("开始IP监控...")
        
        # 加载上次的状态
        state = self._load_state()
        self.current_ip = state.get('last_ip')
        
        if self.current_ip:
            self.logger.info(f"上次记录的IP: {self.current_ip}")
        
        while self.running:
            try:
                # 获取当前IP
                new_ip = self.get_wan_ip()
                
                if new_ip and new_ip != self.current_ip:
                    self.logger.info(f"检测到IP变化: {self.current_ip} -> {new_ip}")
                    
                    # 更新Coturn配置
                    if self.update_coturn_config(new_ip):
                        # 重启Coturn服务
                        if self.restart_coturn():
                            self.current_ip = new_ip
                            
                            # 保存状态
                            state = {
                                'last_ip': new_ip,
                                'last_update': datetime.now().isoformat(),
                                'update_count': state.get('update_count', 0) + 1
                            }
                            self._save_state(state)
                            
                            self.logger.info(f"IP更新完成: {new_ip}")
                        else:
                            self.logger.error("Coturn服务重启失败，跳过此次更新")
                    else:
                        self.logger.error("Coturn配置更新失败")
                
                elif new_ip == self.current_ip:
                    self.logger.debug(f"IP未变化: {new_ip}")
                
                elif not new_ip:
                    self.logger.warning("无法获取WAN IP地址")
                
                # 等待下次检查
                time.sleep(self.config['monitoring']['check_interval'])
                
            except KeyboardInterrupt:
                self.logger.info("收到中断信号，正在停止...")
                break
            except Exception as e:
                self.logger.error(f"监控循环中发生错误: {e}")
                time.sleep(30)  # 错误后等待30秒再重试
        
        self.logger.info("IP监控已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RouterOS IP监控器')
    parser.add_argument(
        '-c', '--config',
        help='配置文件路径',
        default='/opt/matrix/configs/ip-monitor.yaml'
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help='测试模式：只获取IP不更新配置'
    )
    
    args = parser.parse_args()
    
    try:
        monitor = IPMonitor(args.config)
        
        if args.test:
            # 测试模式
            ip = monitor.get_wan_ip()
            if ip:
                print(f"当前WAN IP: {ip}")
            else:
                print("无法获取WAN IP")
        else:
            # 正常运行
            monitor.run()
            
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
