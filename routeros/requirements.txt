# RouterOS API Python依赖包
# 适用于Debian 12虚拟环境

# RouterOS API客户端
routeros-api==0.17.0

# 网络请求库
requests>=2.28.0

# 配置文件处理
PyYAML>=6.0

# 日志处理
colorlog>=6.7.0

# 系统工具
psutil>=5.9.0

# 时间处理
python-dateutil>=2.8.0

# JSON处理增强
ujson>=5.6.0

# 异步支持 (可选)
asyncio-mqtt>=0.11.0

# 错误重试机制
tenacity>=8.2.0

# 配置验证
cerberus>=1.3.4

# 系统监控
py-cpuinfo>=9.0.0

# 网络工具
netifaces>=0.11.0

# 加密支持
cryptography>=3.4.8

# 环境变量处理
python-dotenv>=1.0.0
