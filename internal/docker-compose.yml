# Matrix Synapse Docker Compose 配置
# 适用于内网环境，使用SQLite数据库
# 包含Synapse、Coturn和Nginx服务

services:
  # Matrix Synapse 主服务
  # 官方推荐镜像源 (优先使用)
  synapse:
    image: ghcr.io/element-hq/synapse:latest
    # 备用镜像源 (网络问题时使用):
    # image: matrixdotorg/synapse:latest
    # image: docker.mirrors.ustc.edu.cn/matrixdotorg/synapse:latest
    container_name: matrix-synapse
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      - SYNAPSE_SERVER_NAME=${MATRIX_DOMAIN}
      - SYNAPSE_REPORT_STATS=no
      - SYNAPSE_CONFIG_DIR=/data
      - SYNAPSE_CONFIG_PATH=/data/homeserver.yaml
      - UID=991
      - GID=991
    
    # 卷挂载
    volumes:
      # 主要数据目录 (包含SQLite数据库)
      - ./data:/data
      # 配置文件
      - ./configs/homeserver.yaml:/data/homeserver.yaml:ro
      - ./configs/log.config:/data/log.config:ro
      # SSL证书
      - ./certs:/certs:ro
      # 媒体存储
      - ./data/media:/data/media_store
      # 上传临时目录
      - ./data/uploads:/data/uploads
    
    # 端口映射 (仅内部访问)
    ports:
      - "127.0.0.1:8008:8008"
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 依赖服务
    depends_on:
      - coturn
    
    # 网络配置
    networks:
      - matrix-net
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M

  # Coturn TURN/STUN 服务器
  coturn:
    image: coturn/coturn:latest
    container_name: matrix-coturn
    restart: unless-stopped
    
    # 网络模式 (需要访问真实IP)
    network_mode: host
    
    # 卷挂载
    volumes:
      # Coturn配置文件
      - ./configs/coturn.conf:/etc/coturn/turnserver.conf:ro
      # SSL证书 (TURN子域名证书)
      - ./certs/turn:/certs/turn:ro
      # 所有证书目录 (用于证书管理)
      - ./certs:/certs:ro
      # 日志目录
      - ./logs/coturn:/var/log/coturn
    
    # 环境变量
    environment:
      - DETECT_EXTERNAL_IP=yes
    
    # 健康检查
    healthcheck:
      test: ["CMD", "turnutils_uclient", "-t", "127.0.0.1", "-p", "3478"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: matrix-nginx
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "${HTTP_PORT:-8080}:80"
      - "${HTTPS_PORT:-8443}:443"
    
    # 卷挂载
    volumes:
      # Nginx配置
      - ./configs/nginx.conf:/etc/nginx/nginx.conf:ro
      # SSL证书 (所有子域名证书)
      - ./certs:/certs:ro
      # 日志目录
      - ./logs/nginx:/var/log/nginx
      # 静态文件 (可选)
      - ./static:/var/www/html:ro
    
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 依赖服务
    depends_on:
      - synapse
    
    # 网络配置
    networks:
      - matrix-net

  # 可选: Redis (用于缓存，提升性能)
  redis:
    image: redis:alpine
    container_name: matrix-redis
    restart: unless-stopped
    
    # 命令参数
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    # 卷挂载
    volumes:
      - ./data/redis:/data
    
    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 网络配置
    networks:
      - matrix-net
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 64M

# 网络配置
networks:
  matrix-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷配置 (可选，用于数据持久化)
volumes:
  synapse-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  
  synapse-media:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/media
  
  nginx-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/nginx
  
  coturn-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/coturn

# 配置说明:
# 1. Synapse使用SQLite数据库，数据存储在./data目录
# 2. Coturn使用host网络模式以获取真实IP地址
# 3. Nginx作为反向代理处理SSL终止
# 4. Redis用于缓存，可选但推荐
# 5. 所有服务都配置了健康检查
# 6. 资源限制防止内存过度使用
# 7. 日志统一存储在./logs目录

# 端口说明:
# - 8008: Synapse HTTP (仅内部)
# - 8080/8443: Nginx HTTP/HTTPS (可配置)
# - 3478: Coturn STUN/TURN
# - 65335-65535: Coturn UDP媒体中继端口范围

# 使用方法:
# 1. 复制此文件到安装目录
# 2. 配置环境变量或修改端口设置
# 3. 确保配置文件和证书已准备就绪
# 4. 运行: docker compose up -d
# 5. 查看日志: docker compose logs -f
