#!/bin/bash

# Matrix Synapse 自动化部署脚本
# 适用于 Debian 12，内网环境，支持自定义端口
# 作者: Matrix Deploy Project
# 版本: 1.0.0
# 日期: 2025-07-13

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要以root用户身份运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    
    if [[ ! -f /etc/debian_version ]]; then
        log_error "此脚本仅支持Debian系统"
        exit 1
    fi
    
    DEBIAN_VERSION=$(cat /etc/debian_version | cut -d. -f1)
    if [[ $DEBIAN_VERSION -lt 12 ]]; then
        log_warn "建议使用Debian 12或更高版本"
        read -p "是否继续安装? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_info "系统检查通过: Debian $DEBIAN_VERSION"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    Matrix Synapse 自动化部署脚本 v1.0.0"
    echo "=================================================="
    echo -e "${NC}"
    echo "本脚本将帮助您在Debian 12上部署Matrix Synapse服务器"
    echo "包含以下功能:"
    echo "  • Synapse + Coturn Docker部署"
    echo "  • acme.sh + Cloudflare证书管理"
    echo "  • RouterOS集成 (可选)"
    echo "  • 自动备份和监控"
    echo "  • 安全配置 (可选)"
    echo ""
    echo -e "${YELLOW}注意: 请确保您已经配置好域名DNS解析${NC}"
    echo ""
    read -p "按Enter键继续..." -r
}

# 收集用户配置
collect_config() {
    log_info "开始收集配置信息..."
    
    # 域名配置
    while true; do
        read -p "请输入您的基础域名 (例如: example.com): " BASE_DOMAIN
        if [[ $BASE_DOMAIN =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            break
        else
            log_error "域名格式不正确，请重新输入"
        fi
    done

    # 子域名配置
    read -p "请输入Matrix子域名前缀 [matrix]: " MATRIX_PREFIX
    MATRIX_PREFIX=${MATRIX_PREFIX:-matrix}
    MATRIX_DOMAIN="$MATRIX_PREFIX.$BASE_DOMAIN"

    read -p "请输入TURN子域名前缀 [turn]: " TURN_PREFIX
    TURN_PREFIX=${TURN_PREFIX:-turn}
    TURN_DOMAIN="$TURN_PREFIX.$BASE_DOMAIN"

    log_info "配置的域名:"
    log_info "  基础域名: $BASE_DOMAIN"
    log_info "  Matrix域名: $MATRIX_DOMAIN"
    log_info "  TURN域名: $TURN_DOMAIN"
    
    # 安装路径
    read -p "请输入安装路径 [/opt/matrix]: " INSTALL_PATH
    INSTALL_PATH=${INSTALL_PATH:-/opt/matrix}
    
    # 端口配置
    read -p "请输入HTTPS端口 [8443]: " HTTPS_PORT
    HTTPS_PORT=${HTTPS_PORT:-8443}
    
    read -p "请输入HTTP端口 [8080]: " HTTP_PORT
    HTTP_PORT=${HTTP_PORT:-8080}
    
    # Coturn端口范围
    read -p "请输入Coturn UDP端口范围起始 [65335]: " COTURN_PORT_START
    COTURN_PORT_START=${COTURN_PORT_START:-65335}
    
    read -p "请输入Coturn UDP端口范围结束 [65535]: " COTURN_PORT_END
    COTURN_PORT_END=${COTURN_PORT_END:-65535}
    
    # 证书邮箱
    read -p "请输入证书邮箱 [acme@$BASE_DOMAIN]: " CERT_EMAIL
    CERT_EMAIL=${CERT_EMAIL:-acme@$BASE_DOMAIN}
    
    # Cloudflare API Token
    while true; do
        read -s -p "请输入Cloudflare API Token: " CF_TOKEN
        echo
        if [[ -n $CF_TOKEN ]]; then
            break
        else
            log_error "API Token不能为空"
        fi
    done
    
    # Matrix配置
    echo ""
    log_info "Matrix服务器配置:"
    
    read -p "是否启用用户注册? (y/N): " -n 1 -r ENABLE_REGISTRATION
    echo
    ENABLE_REGISTRATION=${ENABLE_REGISTRATION:-n}
    
    read -p "是否启用联邦功能? (Y/n): " -n 1 -r ENABLE_FEDERATION
    echo
    ENABLE_FEDERATION=${ENABLE_FEDERATION:-y}
    
    # 可选功能
    echo ""
    log_info "可选功能配置:"
    
    read -p "是否配置防火墙 (UFW)? (y/N): " -n 1 -r SETUP_FIREWALL
    echo
    SETUP_FIREWALL=${SETUP_FIREWALL:-n}
    
    read -p "是否安装fail2ban? (y/N): " -n 1 -r INSTALL_FAIL2BAN
    echo
    INSTALL_FAIL2BAN=${INSTALL_FAIL2BAN:-n}
    
    read -p "是否配置RouterOS集成? (y/N): " -n 1 -r SETUP_ROUTEROS
    echo
    SETUP_ROUTEROS=${SETUP_ROUTEROS:-n}
    
    if [[ $SETUP_ROUTEROS =~ ^[Yy]$ ]]; then
        read -p "RouterOS IP地址 [***********]: " ROUTEROS_IP
        ROUTEROS_IP=${ROUTEROS_IP:-***********}
        
        read -p "RouterOS API用户名 [api]: " ROUTEROS_USER
        ROUTEROS_USER=${ROUTEROS_USER:-api}
        
        read -s -p "RouterOS API密码 [api]: " ROUTEROS_PASS
        ROUTEROS_PASS=${ROUTEROS_PASS:-api}
        echo
        
        read -p "WAN接口名称 [WAN]: " WAN_INTERFACE
        WAN_INTERFACE=${WAN_INTERFACE:-WAN}
    fi
}

# 显示配置摘要
show_config_summary() {
    echo ""
    log_info "配置摘要:"
    echo "=================================="
    echo "基础域名: $BASE_DOMAIN"
    echo "Matrix域名: $MATRIX_DOMAIN"
    echo "TURN域名: $TURN_DOMAIN"
    echo "安装路径: $INSTALL_PATH"
    echo "HTTPS端口: $HTTPS_PORT"
    echo "HTTP端口: $HTTP_PORT"
    echo "Coturn端口: $COTURN_PORT_START-$COTURN_PORT_END"
    echo "证书邮箱: $CERT_EMAIL"
    echo "用户注册: $([ $ENABLE_REGISTRATION = 'y' ] && echo '启用' || echo '禁用')"
    echo "联邦功能: $([ $ENABLE_FEDERATION = 'y' ] && echo '启用' || echo '禁用')"
    echo "防火墙: $([ $SETUP_FIREWALL = 'y' ] && echo '配置' || echo '跳过')"
    echo "fail2ban: $([ $INSTALL_FAIL2BAN = 'y' ] && echo '安装' || echo '跳过')"
    echo "RouterOS: $([ $SETUP_ROUTEROS = 'y' ] && echo '配置' || echo '跳过')"
    if [[ $SETUP_ROUTEROS =~ ^[Yy]$ ]]; then
        echo "  - RouterOS IP: $ROUTEROS_IP"
        echo "  - API用户: $ROUTEROS_USER"
        echo "  - WAN接口: $WAN_INTERFACE"
    fi
    echo "=================================="
    echo ""
    
    read -p "配置正确吗? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_info "重新配置..."
        collect_config
        show_config_summary
    fi
}

# 安装系统依赖
install_dependencies() {
    log_info "更新系统包列表..."
    apt update
    
    log_info "安装基础依赖..."
    apt install -y \
        curl \
        wget \
        git \
        unzip \
        gnupg \
        lsb-release \
        ca-certificates \
        apt-transport-https \
        software-properties-common \
        python3 \
        python3-pip \
        python3-venv \
        cron \
        logrotate
    
    log_info "安装Docker..."
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        apt update
        apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
        systemctl enable docker
        systemctl start docker
    else
        log_info "Docker已安装"
    fi
    
    log_info "安装acme.sh..."
    if [[ ! -d /root/.acme.sh ]]; then
        curl https://get.acme.sh | sh -s email=$CERT_EMAIL
        source /root/.bashrc
    else
        log_info "acme.sh已安装"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."

    mkdir -p $INSTALL_PATH/{data,logs,configs,certs,backups}
    mkdir -p $INSTALL_PATH/data/{media,uploads}

    # 设置权限
    chown -R 991:991 $INSTALL_PATH/data
    chmod -R 755 $INSTALL_PATH

    log_info "目录结构创建完成"
}

# 生成配置文件
generate_configs() {
    log_info "生成配置文件..."

    # 生成docker-compose.yml
    cat > $INSTALL_PATH/docker-compose.yml << EOF
version: '3.8'

services:
  synapse:
    image: ghcr.io/element-hq/synapse:latest
    container_name: matrix-synapse
    restart: unless-stopped
    environment:
      - SYNAPSE_SERVER_NAME=$MATRIX_DOMAIN
      - SYNAPSE_REPORT_STATS=no
    volumes:
      - $INSTALL_PATH/data:/data
      - $INSTALL_PATH/configs/homeserver.yaml:/data/homeserver.yaml:ro
      - $INSTALL_PATH/certs:/certs:ro
    ports:
      - "127.0.0.1:8008:8008"
    depends_on:
      - coturn
    networks:
      - matrix-net

  coturn:
    image: coturn/coturn:latest
    container_name: matrix-coturn
    restart: unless-stopped
    ports:
      - "3478:3478"
      - "3478:3478/udp"
      - "$COTURN_PORT_START-$COTURN_PORT_END:$COTURN_PORT_START-$COTURN_PORT_END/udp"
    volumes:
      - $INSTALL_PATH/configs/coturn.conf:/etc/coturn/turnserver.conf:ro
      - $INSTALL_PATH/certs:/certs:ro
    networks:
      - matrix-net

  nginx:
    image: nginx:alpine
    container_name: matrix-nginx
    restart: unless-stopped
    ports:
      - "$HTTP_PORT:80"
      - "$HTTPS_PORT:443"
    volumes:
      - $INSTALL_PATH/configs/nginx.conf:/etc/nginx/nginx.conf:ro
      - $INSTALL_PATH/certs:/certs:ro
    depends_on:
      - synapse
    networks:
      - matrix-net

networks:
  matrix-net:
    driver: bridge
EOF

    log_info "Docker Compose配置生成完成"
}

# 智能SSL证书配置
setup_certificates() {
    log_info "配置SSL证书..."

    # 设置环境变量
    export CF_TOKEN="$CF_TOKEN"
    export INSTALL_PATH="$INSTALL_PATH"
    export CERT_EMAIL="$CERT_EMAIL"

    # 创建证书目录
    mkdir -p $INSTALL_PATH/certs/{matrix,turn,base}

    # 使用智能证书管理脚本
    log_info "使用智能证书管理，检查现有证书有效期..."

    if [[ -f "$INSTALL_PATH/scripts/certificate-manager.sh" ]]; then
        # 使用已安装的证书管理脚本
        if $INSTALL_PATH/scripts/certificate-manager.sh smart "$BASE_DOMAIN" "$MATRIX_DOMAIN" "$TURN_DOMAIN"; then
            log_info "智能证书管理完成"
        else
            log_error "智能证书管理失败，尝试传统方式..."
            setup_certificates_fallback
        fi
    else
        # 使用当前目录的证书管理脚本
        if scripts/certificate-manager.sh smart "$BASE_DOMAIN" "$MATRIX_DOMAIN" "$TURN_DOMAIN"; then
            log_info "智能证书管理完成"
        else
            log_error "智能证书管理失败，尝试传统方式..."
            setup_certificates_fallback
        fi
    fi

    log_info "SSL证书配置完成"
}

# 传统证书申请方式（备用）
setup_certificates_fallback() {
    log_warn "使用传统证书申请方式..."

    # 设置Cloudflare API
    export CF_Token="$CF_TOKEN"

    log_info "申请Matrix域名证书: $MATRIX_DOMAIN"
    # 申请Matrix证书
    /root/.acme.sh/acme.sh --issue --dns dns_cf -d $MATRIX_DOMAIN

    # 安装Matrix证书
    /root/.acme.sh/acme.sh --install-cert -d $MATRIX_DOMAIN \
        --cert-file $INSTALL_PATH/certs/matrix/cert.pem \
        --key-file $INSTALL_PATH/certs/matrix/key.pem \
        --fullchain-file $INSTALL_PATH/certs/matrix/fullchain.pem \
        --ca-file $INSTALL_PATH/certs/matrix/ca.pem

    log_info "申请TURN域名证书: $TURN_DOMAIN"
    # 申请TURN证书
    /root/.acme.sh/acme.sh --issue --dns dns_cf -d $TURN_DOMAIN

    # 安装TURN证书
    /root/.acme.sh/acme.sh --install-cert -d $TURN_DOMAIN \
        --cert-file $INSTALL_PATH/certs/turn/cert.pem \
        --key-file $INSTALL_PATH/certs/turn/key.pem \
        --fullchain-file $INSTALL_PATH/certs/turn/fullchain.pem \
        --ca-file $INSTALL_PATH/certs/turn/ca.pem

    log_info "申请基础域名证书: $BASE_DOMAIN"
    # 申请基础域名证书
    /root/.acme.sh/acme.sh --issue --dns dns_cf -d $BASE_DOMAIN

    # 安装基础域名证书
    /root/.acme.sh/acme.sh --install-cert -d $BASE_DOMAIN \
        --cert-file $INSTALL_PATH/certs/base/cert.pem \
        --key-file $INSTALL_PATH/certs/base/key.pem \
        --fullchain-file $INSTALL_PATH/certs/base/fullchain.pem \
        --ca-file $INSTALL_PATH/certs/base/ca.pem \
        --reloadcmd "docker compose -f $INSTALL_PATH/docker-compose.yml restart nginx"

    # 设置证书权限
    chmod -R 644 $INSTALL_PATH/certs/
    find $INSTALL_PATH/certs/ -type d -exec chmod 755 {} \;

    log_warn "传统证书申请完成"
}

# 配置防火墙
setup_firewall() {
    if [[ $SETUP_FIREWALL =~ ^[Yy]$ ]]; then
        log_info "配置UFW防火墙..."

        # 安装UFW
        apt install -y ufw

        # 基础规则
        ufw --force reset
        ufw default deny incoming
        ufw default allow outgoing

        # 允许SSH
        ufw allow ssh

        # 允许Matrix端口
        ufw allow $HTTP_PORT
        ufw allow $HTTPS_PORT
        ufw allow 3478
        ufw allow $COTURN_PORT_START:$COTURN_PORT_END/udp

        # 启用防火墙
        ufw --force enable

        log_info "防火墙配置完成"
    fi
}

# 安装fail2ban
setup_fail2ban() {
    if [[ $INSTALL_FAIL2BAN =~ ^[Yy]$ ]]; then
        log_info "安装fail2ban..."

        apt install -y fail2ban

        # 配置fail2ban
        cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true
port = http,https
logpath = $INSTALL_PATH/logs/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = $INSTALL_PATH/logs/nginx/error.log
maxretry = 10
EOF

        systemctl enable fail2ban
        systemctl start fail2ban

        log_info "fail2ban安装完成"
    fi
}

# 生成Synapse配置
generate_synapse_config() {
    log_info "生成Synapse配置文件..."

    # 生成密钥
    MACAROON_SECRET=$(openssl rand -hex 32)
    REGISTRATION_SECRET=$(openssl rand -hex 32)
    FORM_SECRET=$(openssl rand -hex 32)
    TURN_SECRET=$(openssl rand -hex 32)

    # 替换配置模板
    # 设置联邦配置
    FEDERATION_CONFIG=""
    if [[ $ENABLE_FEDERATION =~ ^[Nn]$ ]]; then
        FEDERATION_CONFIG="# federation_domain_whitelist: []"
    fi

    sed -e "s/{{DOMAIN_NAME}}/$MATRIX_DOMAIN/g" \
        -e "s/{{TURN_DOMAIN}}/$TURN_DOMAIN/g" \
        -e "s/{{ENABLE_REGISTRATION}}/$([[ $ENABLE_REGISTRATION =~ ^[Yy]$ ]] && echo 'true' || echo 'false')/g" \
        -e "s/{{FEDERATION_CONFIG}}/$FEDERATION_CONFIG/g" \
        -e "s/{{MACAROON_SECRET}}/$MACAROON_SECRET/g" \
        -e "s/{{REGISTRATION_SECRET}}/$REGISTRATION_SECRET/g" \
        -e "s/{{FORM_SECRET}}/$FORM_SECRET/g" \
        -e "s/{{TURN_SECRET}}/$TURN_SECRET/g" \
        configs/homeserver.yaml.template > $INSTALL_PATH/configs/homeserver.yaml

    # 生成Coturn配置
    sed -e "s/{{DOMAIN_NAME}}/$TURN_DOMAIN/g" \
        -e "s/{{TURN_SECRET}}/$TURN_SECRET/g" \
        -e "s/{{COTURN_PORT_START}}/$COTURN_PORT_START/g" \
        -e "s/{{COTURN_PORT_END}}/$COTURN_PORT_END/g" \
        -e "s/{{EXTERNAL_IP}}/127.0.0.1/g" \
        configs/coturn.conf.template > $INSTALL_PATH/configs/coturn.conf

    # 生成Nginx配置
    sed -e "s/{{BASE_DOMAIN}}/$BASE_DOMAIN/g" \
        -e "s/{{MATRIX_DOMAIN}}/$MATRIX_DOMAIN/g" \
        -e "s/{{TURN_DOMAIN}}/$TURN_DOMAIN/g" \
        -e "s/{{HTTPS_PORT}}/$HTTPS_PORT/g" \
        -e "s/{{HTTP_PORT}}/$HTTP_PORT/g" \
        -e "s/{{COTURN_PORT_START}}/$COTURN_PORT_START/g" \
        -e "s/{{COTURN_PORT_END}}/$COTURN_PORT_END/g" \
        configs/nginx.conf.template > $INSTALL_PATH/configs/nginx.conf

    # 生成日志配置
    cp configs/log.config.template $INSTALL_PATH/configs/log.config

    log_info "配置文件生成完成"
}

# 启动服务
start_services() {
    log_info "启动Matrix服务..."

    cd $INSTALL_PATH

    # 生成Synapse密钥
    if [[ ! -f "$INSTALL_PATH/data/$DOMAIN_NAME.signing.key" ]]; then
        log_info "生成Synapse签名密钥..."
        docker run --rm \
            -v $INSTALL_PATH/data:/data \
            -v $INSTALL_PATH/configs/homeserver.yaml:/data/homeserver.yaml:ro \
            ghcr.io/element-hq/synapse:latest \
            generate
    fi

    # 启动服务
    docker compose up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30

    # 检查服务状态
    if docker compose ps | grep -q "Up"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        docker compose logs
        exit 1
    fi
}

# 配置RouterOS集成
setup_routeros_integration() {
    if [[ $SETUP_ROUTEROS =~ ^[Yy]$ ]]; then
        log_info "配置RouterOS集成..."

        # 创建Python虚拟环境
        python3 -m venv $INSTALL_PATH/venv
        source $INSTALL_PATH/venv/bin/activate

        # 安装依赖
        pip install -r routeros/requirements.txt

        # 生成配置文件
        cat > $INSTALL_PATH/configs/ip-monitor.yaml << EOF
routeros:
  host: "$ROUTEROS_IP"
  username: "$ROUTEROS_USER"
  password: "$ROUTEROS_PASS"
  port: 8728
  wan_interface: "$WAN_INTERFACE"

coturn:
  config_path: "$INSTALL_PATH/configs/coturn.conf"
  docker_service: "matrix-coturn"
  compose_path: "$INSTALL_PATH/docker-compose.yml"

monitoring:
  check_interval: 60
  log_level: "INFO"
  log_file: "$INSTALL_PATH/logs/ip-monitor.log"
  state_file: "$INSTALL_PATH/data/ip-state.json"
EOF

        # 复制监控脚本
        cp routeros/ip-monitor.py $INSTALL_PATH/scripts/
        chmod +x $INSTALL_PATH/scripts/ip-monitor.py

        # 设置定时任务
        (crontab -l 2>/dev/null; echo "* * * * * $INSTALL_PATH/venv/bin/python $INSTALL_PATH/scripts/ip-monitor.py -c $INSTALL_PATH/configs/ip-monitor.yaml") | crontab -

        log_info "RouterOS集成配置完成"
    fi
}

# 配置备份和监控
setup_backup_monitoring() {
    log_info "配置备份和监控..."

    # 复制脚本
    cp scripts/*.sh $INSTALL_PATH/scripts/
    chmod +x $INSTALL_PATH/scripts/*.sh

    # 设置定时任务
    (crontab -l 2>/dev/null; echo "0 2 * * * $INSTALL_PATH/scripts/backup.sh") | crontab -
    (crontab -l 2>/dev/null; echo "0 * * * * $INSTALL_PATH/scripts/health-check.sh") | crontab -
    # 智能证书续期 - 每天凌晨3点检查
    (crontab -l 2>/dev/null; echo "0 3 * * * CF_TOKEN=$CF_TOKEN INSTALL_PATH=$INSTALL_PATH $INSTALL_PATH/scripts/certificate-manager.sh renew") | crontab -

    log_info "备份和监控配置完成"
}

# 显示部署结果
show_deployment_result() {
    echo ""
    log_info "Matrix Synapse部署完成！"
    echo "=================================="
    echo "服务访问地址:"
    echo "  Matrix客户端: https://$MATRIX_DOMAIN:$HTTPS_PORT"
    echo "  管理界面: https://$MATRIX_DOMAIN:$HTTPS_PORT/_synapse/admin"
    echo "  基础域名: https://$BASE_DOMAIN:$HTTPS_PORT (重定向到Matrix)"
    echo ""
    echo "TURN服务配置:"
    echo "  STUN/TURN: turn://$TURN_DOMAIN:3478"
    echo "  TURN TLS: turns://$TURN_DOMAIN:5349"
    echo "  UDP媒体端口: $COTURN_PORT_START-$COTURN_PORT_END"
    echo "  注意: TURN服务无Web界面，直接通过上述端口访问"
    echo ""
    echo "重要文件位置:"
    echo "  安装目录: $INSTALL_PATH"
    echo "  配置文件: $INSTALL_PATH/configs/"
    echo "  日志文件: $INSTALL_PATH/logs/"
    echo "  备份目录: $INSTALL_PATH/backups/"
    echo ""
    echo "管理命令:"
    echo "  查看服务状态: cd $INSTALL_PATH && docker compose ps"
    echo "  查看日志: cd $INSTALL_PATH && docker compose logs -f"
    echo "  重启服务: cd $INSTALL_PATH && docker compose restart"
    echo "  停止服务: cd $INSTALL_PATH && docker compose down"
    echo ""
    echo "下一步操作:"
    echo "1. 创建管理员用户:"
    echo "   docker exec -it matrix-synapse register_new_matrix_user -c /data/homeserver.yaml"
    echo ""
    echo "2. 配置客户端连接:"
    echo "   服务器: $MATRIX_DOMAIN:$HTTPS_PORT"
    echo "   用户名: @username:$MATRIX_DOMAIN"
    echo ""
    echo "3. 检查服务健康状态:"
    echo "   $INSTALL_PATH/scripts/health-check.sh"
    echo "=================================="

    # 显示端口转发提醒
    echo ""
    log_warn "重要提醒: 请确保路由器已配置以下端口转发:"
    echo "  $HTTP_PORT -> $HTTP_PORT (HTTP)"
    echo "  $HTTPS_PORT -> $HTTPS_PORT (HTTPS)"
    echo "  3478 -> 3478 (STUN/TURN)"
    echo "  $COTURN_PORT_START-$COTURN_PORT_END -> $COTURN_PORT_START-$COTURN_PORT_END (UDP媒体)"
    echo ""
}

# 主函数
main() {
    check_root
    show_welcome
    check_system
    collect_config
    show_config_summary

    log_info "开始安装..."
    install_dependencies
    create_directories
    generate_configs
    generate_synapse_config
    setup_certificates
    setup_firewall
    setup_fail2ban
    start_services
    setup_routeros_integration
    setup_backup_monitoring

    show_deployment_result

    log_info "部署完成！请查看上方的使用说明。"
}

# 执行主函数
main "$@"
