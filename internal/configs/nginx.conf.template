# Nginx 配置文件 - Matrix Synapse 反向代理
# 适用于内网环境，支持自定义端口
# 包含SSL终止和安全配置

# 全局配置
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 事件配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置
http {
    # 基本配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 客户端配置
    client_max_body_size 50M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头配置
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Matrix特定配置
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }
    
    # 上游服务器配置
    upstream synapse {
        server 127.0.0.1:8008;
        keepalive 32;
    }
    
    # HTTP服务器 (用于内网访问和健康检查)
    # 注意: 由于ISP封禁80端口，此服务器主要用于内网访问
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name {{MATRIX_DOMAIN}} {{TURN_DOMAIN}} {{BASE_DOMAIN}};

        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Let's Encrypt验证 (通过DNS验证，此处保留以防需要)
        location /.well-known/acme-challenge/ {
            root /var/www/html;
        }

        # 重定向到HTTPS (使用自定义端口)
        location / {
            return 301 https://$host:{{HTTPS_PORT}}$request_uri;
        }
    }
    
    # Matrix主服务器 (HTTPS)
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name {{MATRIX_DOMAIN}};

        # SSL证书配置 (Matrix子域名证书)
        ssl_certificate /certs/matrix/fullchain.pem;
        ssl_certificate_key /certs/matrix/key.pem;
        
        # SSL安全配置
        ssl_stapling on;
        ssl_stapling_verify on;
        ssl_trusted_certificate /certs/matrix/fullchain.pem;

        # HSTS配置
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        # Matrix客户端API
        location ~ ^(/_matrix|/_synapse/client) {
            proxy_pass http://synapse;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            
            # 超时配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # 缓冲配置
            proxy_buffering off;
            proxy_request_buffering off;
        }
        
        # Matrix联邦API
        location ~ ^(/_matrix/federation|/_matrix/key) {
            proxy_pass http://synapse;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            
            # 联邦超时配置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Matrix媒体API
        location ~ ^/_matrix/media/ {
            proxy_pass http://synapse;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            
            # 媒体文件缓存
            proxy_cache_valid 200 1d;
            proxy_cache_valid 404 1m;
            
            # 大文件支持
            client_max_body_size 50M;
            proxy_request_buffering off;
        }
        
        # Well-known配置 (Matrix联邦和客户端自动发现)
        location /.well-known/matrix/server {
            return 200 '{"m.server": "{{MATRIX_DOMAIN}}:{{HTTPS_PORT}}"}';
            default_type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Cache-Control "public, max-age=3600";
        }

        location /.well-known/matrix/client {
            return 200 '{
                "m.homeserver": {
                    "base_url": "https://{{MATRIX_DOMAIN}}:{{HTTPS_PORT}}"
                },
                "m.identity_server": {
                    "base_url": "https://vector.im"
                },
                "org.matrix.msc3575.proxy": {
                    "url": "https://{{MATRIX_DOMAIN}}:{{HTTPS_PORT}}"
                }
            }';
            default_type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Cache-Control "public, max-age=3600";
        }
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # 静态文件 (可选)
        location / {
            root /var/www/html;
            index index.html;
            try_files $uri $uri/ =404;
        }
        
        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /var/www/html;
        }
    }



    # 基础域名服务器 (用于Well-known和重定向)
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name {{BASE_DOMAIN}};

        # SSL证书配置 (基础域名证书)
        ssl_certificate /certs/base/fullchain.pem;
        ssl_certificate_key /certs/base/key.pem;

        # SSL安全配置
        ssl_stapling on;
        ssl_stapling_verify on;
        ssl_trusted_certificate /certs/base/fullchain.pem;

        # HSTS配置
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Matrix服务发现 (用于基础域名)
        location /.well-known/matrix/server {
            return 200 '{"m.server": "{{MATRIX_DOMAIN}}:{{HTTPS_PORT}}"}';
            default_type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Cache-Control "public, max-age=3600";
        }

        location /.well-known/matrix/client {
            return 200 '{
                "m.homeserver": {
                    "base_url": "https://{{MATRIX_DOMAIN}}:{{HTTPS_PORT}}"
                },
                "m.identity_server": {
                    "base_url": "https://vector.im"
                }
            }';
            default_type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Cache-Control "public, max-age=3600";
        }

        # 重定向到Matrix子域名
        location / {
            return 301 https://{{MATRIX_DOMAIN}}:{{HTTPS_PORT}}$request_uri;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "base-healthy\n";
            add_header Content-Type text/plain;
        }
    }
}

# 配置说明:
# 1. 支持HTTP到HTTPS的自动重定向
# 2. 配置了现代SSL/TLS安全参数
# 3. 包含了Matrix特定的代理配置
# 4. 支持WebSocket连接
# 5. 配置了合理的超时和缓冲参数
# 6. 包含了安全头和HSTS
# 7. 支持媒体文件缓存
# 8. 配置了Well-known端点
# 9. 支持多子域名配置

# 域名说明:
# - {{MATRIX_DOMAIN}}: Matrix Synapse主服务
# - {{BASE_DOMAIN}}: 基础域名，重定向到Matrix
# - {{TURN_DOMAIN}}: TURN服务域名，仅用于SSL证书，无Web界面

# 端口说明:
# - {{HTTP_PORT}}: HTTP端口 (重定向到HTTPS)
# - {{HTTPS_PORT}}: HTTPS端口 (自定义，避免ISP封禁)
# - 8008: Synapse后端端口 (内部)
# - 3478: TURN STUN/TURN端口 (直接访问)
# - 5349: TURN TLS端口 (直接访问)

# TURN服务访问:
# TURN服务不通过nginx代理，直接通过以下端口访问:
# - turn://{{TURN_DOMAIN}}:3478 (STUN/TURN)
# - turns://{{TURN_DOMAIN}}:5349 (TURN over TLS)
# - UDP媒体端口: {{COTURN_PORT_START}}-{{COTURN_PORT_END}}

# 重要提示:
# - 确保所有SSL证书文件存在且可读
# - 定期检查访问日志和错误日志
# - 根据实际负载调整worker_processes
# - 监控所有域名的SSL证书有效期
# - 根据需要调整client_max_body_size
# - TURN服务状态通过Matrix管理界面查看
