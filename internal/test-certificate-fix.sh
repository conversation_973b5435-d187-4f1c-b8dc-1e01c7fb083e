#!/bin/bash

# 证书管理修复验证脚本
# 用于测试修复后的证书管理功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试环境设置
TEST_DIR="/tmp/matrix-cert-test"
TEST_DOMAINS=("example.com" "matrix.example.com" "turn.example.com")

# 清理测试环境
cleanup_test() {
    rm -rf "$TEST_DIR"
}

# 设置测试环境
setup_test() {
    log_info "设置测试环境..."
    
    cleanup_test
    mkdir -p "$TEST_DIR"/{certs/{base,matrix,turn},logs}
    
    # 创建模拟的证书文件
    create_mock_certificate "$TEST_DIR/certs/matrix/fullchain.pem" "matrix.example.com" 60
    create_mock_certificate "$TEST_DIR/certs/turn/fullchain.pem" "turn.example.com" 10
    # base域名证书不存在，用于测试
}

# 创建模拟证书
create_mock_certificate() {
    local cert_file="$1"
    local domain="$2"
    local days_valid="$3"

    # macOS兼容的date命令
    local expiry_date
    if [[ "$OSTYPE" == "darwin"* ]]; then
        expiry_date=$(date -v "+${days_valid}d" "+%b %d %H:%M:%S %Y %Z")
    else
        expiry_date=$(date -d "+${days_valid} days" "+%b %d %H:%M:%S %Y %Z")
    fi
    
    # 创建一个简单的模拟证书（仅用于测试过期时间解析）
    cat > "$cert_file" << EOF
-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKL0UG+jkjkjMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjUwNzEzMDAwMDAwWhcNMjUwOTEwMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuVqVeK+jkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkjkj
-----END CERTIFICATE-----
EOF

    # 使用openssl创建一个真实的测试证书
    openssl req -x509 -newkey rsa:2048 -keyout "${cert_file%.*}.key" -out "$cert_file" \
        -days "$days_valid" -nodes -subj "/CN=$domain" 2>/dev/null || {
        log_warn "无法创建真实证书，使用模拟证书"
    }
}

# 测试证书有效期检查
test_certificate_validity() {
    log_test "测试证书有效期检查功能..."
    
    export INSTALL_PATH="$TEST_DIR"
    
    # 测试有效证书 (60天)
    log_info "测试有效证书 (matrix.example.com, 60天有效期)..."
    if INSTALL_PATH="$TEST_DIR" ./scripts/certificate-manager.sh status example.com matrix.example.com turn.example.com; then
        log_info "✓ 证书状态检查成功"
    else
        log_error "✗ 证书状态检查失败"
        return 1
    fi
    
    echo ""
}

# 测试智能证书管理
test_smart_management() {
    log_test "测试智能证书管理功能..."
    
    # 模拟智能管理（不实际申请证书）
    log_info "测试智能管理模式..."
    
    # 由于没有真实的acme.sh环境，这里主要测试脚本逻辑
    if INSTALL_PATH="$TEST_DIR" ./scripts/certificate-manager.sh help >/dev/null 2>&1; then
        log_info "✓ 智能管理脚本可以正常调用"
    else
        log_error "✗ 智能管理脚本调用失败"
        return 1
    fi
}

# 测试环境变量处理
test_environment_variables() {
    log_test "测试环境变量处理..."
    
    # 测试缺少CF_TOKEN的情况
    log_info "测试缺少CF_TOKEN时的状态检查..."
    if INSTALL_PATH="$TEST_DIR" ./scripts/certificate-manager.sh status example.com matrix.example.com turn.example.com >/dev/null 2>&1; then
        log_info "✓ 状态检查在缺少CF_TOKEN时仍能工作"
    else
        log_error "✗ 状态检查在缺少CF_TOKEN时失败"
        return 1
    fi
}

# 测试deploy.sh集成
test_deploy_integration() {
    log_test "测试与deploy.sh的集成..."
    
    # 检查deploy.sh中的证书管理调用
    if grep -q "CF_TOKEN.*certificate-manager.sh.*smart" deploy.sh; then
        log_info "✓ deploy.sh包含正确的环境变量传递"
    else
        log_error "✗ deploy.sh缺少正确的环境变量传递"
        return 1
    fi
    
    if grep -q "setup_certificates_fallback" deploy.sh; then
        log_info "✓ deploy.sh包含备用证书申请方案"
    else
        log_error "✗ deploy.sh缺少备用证书申请方案"
        return 1
    fi
}

# 主测试函数
main() {
    log_info "开始证书管理修复验证测试..."
    echo "=================================="
    
    local test_count=0
    local pass_count=0
    
    # 设置测试环境
    setup_test
    
    # 运行测试
    tests=(
        "test_certificate_validity"
        "test_smart_management" 
        "test_environment_variables"
        "test_deploy_integration"
    )
    
    for test in "${tests[@]}"; do
        ((test_count++))
        echo ""
        if $test; then
            ((pass_count++))
            log_info "✓ $test 通过"
        else
            log_error "✗ $test 失败"
        fi
    done
    
    # 清理测试环境
    cleanup_test
    
    echo ""
    echo "=================================="
    log_info "测试完成！"
    echo "总测试数: $test_count"
    echo "通过数: $pass_count"
    echo "失败数: $((test_count - pass_count))"
    echo "成功率: $(( pass_count * 100 / test_count ))%"
    
    if [[ $pass_count -eq $test_count ]]; then
        log_info "🎉 所有测试通过！证书管理修复验证成功。"
        return 0
    else
        log_error "❌ 部分测试失败，需要进一步检查。"
        return 1
    fi
}

# 运行测试
main "$@"
