#!/bin/bash

# Docker镜像网络问题修复脚本
# 用于解决Docker镜像拉取失败的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker服务
check_docker() {
    if ! systemctl is-active --quiet docker; then
        log_error "Docker服务未运行"
        log_info "启动Docker服务..."
        sudo systemctl start docker
    fi
    log_info "Docker服务运行正常"
}

# 配置Docker镜像加速器
setup_docker_mirrors() {
    log_info "配置Docker镜像加速器..."
    
    sudo mkdir -p /etc/docker
    
    sudo tee /etc/docker/daemon.json << EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://ccr.ccs.tencentyun.com"
  ],
  "max-concurrent-downloads": 3,
  "max-concurrent-uploads": 5,
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF

    log_info "重启Docker服务..."
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    log_info "验证镜像加速器配置..."
    sudo docker info | grep -A 10 "Registry Mirrors" || log_warn "未找到镜像加速器配置"
}

# 拉取Matrix相关镜像
pull_matrix_images() {
    log_info "拉取Matrix相关镜像..."
    
    # 定义镜像列表
    declare -A IMAGES=(
        ["synapse"]="ghcr.io/element-hq/synapse:latest"
        ["synapse_backup"]="matrixdotorg/synapse:latest"
        ["coturn"]="coturn/coturn:latest"
        ["nginx"]="nginx:alpine"
    )
    
    # 定义镜像加速器前缀
    MIRRORS=(
        ""
        "docker.mirrors.ustc.edu.cn/"
        "hub-mirror.c.163.com/"
        "ccr.ccs.tencentyun.com/"
    )
    
    for image_name in "${!IMAGES[@]}"; do
        image="${IMAGES[$image_name]}"
        log_info "拉取镜像: $image"
        
        success=false
        for mirror in "${MIRRORS[@]}"; do
            if [[ -n "$mirror" ]]; then
                # 使用镜像加速器
                mirror_image="${mirror}${image}"
                log_info "尝试从镜像源拉取: $mirror_image"
                
                if sudo docker pull "$mirror_image"; then
                    # 重新标记为原始镜像名
                    sudo docker tag "$mirror_image" "$image"
                    log_info "✓ 成功拉取并标记: $image"
                    success=true
                    break
                else
                    log_warn "从 $mirror 拉取失败，尝试下一个镜像源..."
                fi
            else
                # 直接拉取
                log_info "尝试直接拉取: $image"
                if sudo docker pull "$image"; then
                    log_info "✓ 成功拉取: $image"
                    success=true
                    break
                else
                    log_warn "直接拉取失败，尝试镜像加速器..."
                fi
            fi
        done
        
        if [[ "$success" == "false" ]]; then
            log_error "无法拉取镜像: $image"
            return 1
        fi
    done
    
    log_info "所有镜像拉取完成！"
}

# 验证镜像
verify_images() {
    log_info "验证镜像..."
    
    declare -A IMAGES=(
        ["synapse"]="ghcr.io/element-hq/synapse:latest"
        ["coturn"]="coturn/coturn:latest"
        ["nginx"]="nginx:alpine"
    )
    
    for image_name in "${!IMAGES[@]}"; do
        image="${IMAGES[$image_name]}"
        if sudo docker images | grep -q "${image%:*}"; then
            log_info "✓ $image 可用"
        else
            log_error "✗ $image 不可用"
            return 1
        fi
    done
    
    log_info "所有镜像验证通过！"
}

# 测试镜像运行
test_images() {
    log_info "测试镜像运行..."
    
    # 测试Synapse镜像
    if sudo docker run --rm ghcr.io/element-hq/synapse:latest --version > /dev/null 2>&1; then
        log_info "✓ Synapse镜像运行正常"
    else
        log_error "✗ Synapse镜像运行失败"
        return 1
    fi
    
    # 测试Nginx镜像
    if sudo docker run --rm nginx:alpine nginx -v > /dev/null 2>&1; then
        log_info "✓ Nginx镜像运行正常"
    else
        log_error "✗ Nginx镜像运行失败"
        return 1
    fi
    
    log_info "镜像测试完成！"
}

# 显示使用说明
show_usage() {
    echo "Docker镜像网络问题修复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  setup     - 配置镜像加速器并拉取镜像"
    echo "  pull      - 仅拉取镜像"
    echo "  verify    - 验证镜像可用性"
    echo "  test      - 测试镜像运行"
    echo "  all       - 执行所有操作 (默认)"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup   # 配置加速器并拉取镜像"
    echo "  $0 pull    # 仅拉取镜像"
    echo "  $0 all     # 执行完整修复流程"
}

# 主函数
main() {
    case "${1:-all}" in
        "setup")
            check_docker
            setup_docker_mirrors
            pull_matrix_images
            ;;
        "pull")
            check_docker
            pull_matrix_images
            ;;
        "verify")
            verify_images
            ;;
        "test")
            test_images
            ;;
        "all")
            check_docker
            setup_docker_mirrors
            pull_matrix_images
            verify_images
            test_images
            log_info "Docker镜像修复完成！现在可以继续Matrix部署。"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
