#!/bin/bash

# Matrix Synapse 自动部署系统 - 用户友好菜单界面
# 符合中文优先原则和用户体验要求

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${WHITE}           Matrix Synapse 自动部署系统 v1.0.0${NC}"
    echo -e "${WHITE}              企业级Matrix服务器部署解决方案${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

# 显示主菜单
show_main_menu() {
    show_header
    echo -e "${WHITE}请选择要执行的操作：${NC}"
    echo ""
    echo -e "  ${GREEN}1${NC}. 完整部署 (一键部署Matrix服务器)"
    echo -e "  ${GREEN}2${NC}. 证书管理 (SSL证书申请和管理)"
    echo -e "  ${GREEN}3${NC}. 系统维护 (备份、监控、健康检查)"
    echo -e "  ${GREEN}4${NC}. 配置管理 (修改和更新配置)"
    echo -e "  ${GREEN}5${NC}. 用户管理 (Matrix用户创建和管理)"
    echo -e "  ${GREEN}6${NC}. 故障排除 (诊断和问题解决)"
    echo -e "  ${GREEN}7${NC}. 系统信息 (查看状态和版本信息)"
    echo -e "  ${GREEN}8${NC}. 帮助文档 (使用指南和技术支持)"
    echo ""
    echo -e "  ${RED}0${NC}. 退出系统"
    echo ""
    echo -e "${CYAN}================================================================${NC}"
}

# 显示证书管理菜单
show_certificate_menu() {
    show_header
    echo -e "${WHITE}证书管理 - SSL证书申请和管理${NC}"
    echo ""
    echo -e "  ${GREEN}1${NC}. 首次证书申请 (申请新的SSL证书)"
    echo -e "  ${GREEN}2${NC}. 证书状态检查 (查看证书有效期和状态)"
    echo -e "  ${GREEN}3${NC}. 证书手动续期 (手动续期即将过期的证书)"
    echo -e "  ${GREEN}4${NC}. 证书软链接管理 (创建和验证软链接)"
    echo -e "  ${GREEN}5${NC}. 证书路径验证 (验证证书路径配置)"
    echo -e "  ${GREEN}6${NC}. 智能证书管理 (推荐：自动检测和管理)"
    echo -e "  ${GREEN}7${NC}. 强制重新申请 (谨慎使用：强制申请新证书)"
    echo ""
    echo -e "  ${RED}0${NC}. 返回主菜单"
    echo ""
    echo -e "${CYAN}================================================================${NC}"
}

# 显示系统维护菜单
show_maintenance_menu() {
    show_header
    echo -e "${WHITE}系统维护 - 备份、监控和健康检查${NC}"
    echo ""
    echo -e "  ${GREEN}1${NC}. 系统备份 (备份配置文件和数据)"
    echo -e "  ${GREEN}2${NC}. 健康检查 (检查所有服务状态)"
    echo -e "  ${GREEN}3${NC}. 日志查看 (查看系统和应用日志)"
    echo -e "  ${GREEN}4${NC}. 性能监控 (查看系统资源使用情况)"
    echo -e "  ${GREEN}5${NC}. 服务重启 (重启Matrix相关服务)"
    echo -e "  ${GREEN}6${NC}. 数据库维护 (SQLite数据库优化)"
    echo -e "  ${GREEN}7${NC}. 清理临时文件 (清理日志和临时文件)"
    echo ""
    echo -e "  ${RED}0${NC}. 返回主菜单"
    echo ""
    echo -e "${CYAN}================================================================${NC}"
}

# 输入验证函数
validate_input() {
    local input="$1"
    local min="$2"
    local max="$3"
    
    if [[ ! "$input" =~ ^[0-9]+$ ]]; then
        return 1
    fi
    
    if [[ "$input" -lt "$min" || "$input" -gt "$max" ]]; then
        return 1
    fi
    
    return 0
}

# 获取用户输入
get_user_choice() {
    local min="$1"
    local max="$2"
    local choice
    
    while true; do
        echo -n -e "${WHITE}请输入选项 (${min}-${max}, 0=返回/退出): ${NC}"
        read -r choice
        
        if validate_input "$choice" 0 "$max"; then
            echo "$choice"
            return 0
        else
            log_error "无效输入！请输入 ${min}-${max} 之间的数字，或输入 0 返回/退出。"
            echo ""
        fi
    done
}

# 确认操作
confirm_action() {
    local message="$1"
    local choice
    
    echo ""
    echo -e "${YELLOW}$message${NC}"
    echo -n -e "${WHITE}确认执行吗？(y/N): ${NC}"
    read -r choice
    
    case "$choice" in
        [Yy]|[Yy][Ee][Ss]|是|确认)
            return 0
            ;;
        *)
            log_info "操作已取消。"
            return 1
            ;;
    esac
}

# 暂停等待用户
pause_for_user() {
    echo ""
    echo -n -e "${WHITE}按任意键继续...${NC}"
    read -n 1 -s
    echo ""
}

# 执行完整部署
execute_full_deployment() {
    show_header
    echo -e "${WHITE}完整部署 - 一键部署Matrix服务器${NC}"
    echo ""
    
    if confirm_action "这将执行完整的Matrix Synapse部署流程，包括证书申请、服务配置和启动。"; then
        log_info "开始执行完整部署..."
        
        # 检查配置文件
        if [[ ! -f "$PROJECT_ROOT/config.env" ]]; then
            log_error "配置文件 config.env 不存在！"
            log_info "请先复制 config-example.env 为 config.env 并填写配置信息。"
            pause_for_user
            return 1
        fi
        
        # 执行部署脚本
        cd "$PROJECT_ROOT"
        if ./deploy.sh; then
            log_success "Matrix Synapse部署完成！"
        else
            log_error "部署过程中出现错误，请检查日志。"
        fi
        
        pause_for_user
    fi
}

# 证书管理主函数
handle_certificate_management() {
    while true; do
        show_certificate_menu
        local choice
        choice=$(get_user_choice 1 7)
        
        case "$choice" in
            0)
                return 0
                ;;
            1)
                execute_certificate_apply
                ;;
            2)
                execute_certificate_status
                ;;
            3)
                execute_certificate_renew
                ;;
            4)
                execute_certificate_symlinks
                ;;
            5)
                execute_certificate_verify_paths
                ;;
            6)
                execute_certificate_smart
                ;;
            7)
                execute_certificate_force
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 主程序循环
main() {
    # 检查运行环境
    if [[ $EUID -eq 0 ]]; then
        log_warn "检测到以root用户运行，建议使用sudo执行特定操作。"
    fi
    
    # 检查项目目录
    if [[ ! -d "$PROJECT_ROOT" ]]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 主菜单循环
    while true; do
        show_main_menu
        local choice
        choice=$(get_user_choice 1 8)
        
        case "$choice" in
            0)
                echo ""
                log_info "感谢使用 Matrix Synapse 自动部署系统！"
                exit 0
                ;;
            1)
                execute_full_deployment
                ;;
            2)
                handle_certificate_management
                ;;
            3)
                handle_system_maintenance
                ;;
            4)
                handle_configuration_management
                ;;
            5)
                handle_user_management
                ;;
            6)
                handle_troubleshooting
                ;;
            7)
                show_system_information
                ;;
            8)
                show_help_documentation
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
