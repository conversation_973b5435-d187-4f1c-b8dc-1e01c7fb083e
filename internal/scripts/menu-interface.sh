#!/bin/bash

# Matrix Synapse 自动部署系统 - 用户友好菜单界面
# 符合中文优先原则和用户体验要求
# 版本: 1.0.0

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${WHITE}           Matrix Synapse 自动部署系统 v1.0.0${NC}"
    echo -e "${WHITE}              企业级Matrix服务器部署解决方案${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

# 显示主菜单
show_main_menu() {
    show_header
    echo -e "${WHITE}请选择要执行的操作：${NC}"
    echo ""
    echo -e "  ${GREEN}1${NC}. 完整部署 (一键部署Matrix服务器)"
    echo -e "  ${GREEN}2${NC}. 证书管理 (SSL证书申请和管理)"
    echo -e "  ${GREEN}3${NC}. 系统维护 (备份、监控、健康检查)"
    echo -e "  ${GREEN}4${NC}. 配置管理 (修改和更新配置)"
    echo -e "  ${GREEN}5${NC}. 用户管理 (Matrix用户创建和管理)"
    echo -e "  ${GREEN}6${NC}. 故障排除 (诊断和问题解决)"
    echo -e "  ${GREEN}7${NC}. 系统信息 (查看状态和版本信息)"
    echo -e "  ${GREEN}8${NC}. 帮助文档 (使用指南和技术支持)"
    echo ""
    echo -e "  ${RED}0${NC}. 退出系统"
    echo ""
    echo -e "${CYAN}================================================================${NC}"
}

# 显示证书管理菜单
show_certificate_menu() {
    show_header
    echo -e "${WHITE}证书管理 - SSL证书申请和管理${NC}"
    echo ""
    echo -e "  ${GREEN}1${NC}. 首次证书申请 (申请新的SSL证书)"
    echo -e "  ${GREEN}2${NC}. 证书状态检查 (查看证书有效期和状态)"
    echo -e "  ${GREEN}3${NC}. 证书手动续期 (手动续期即将过期的证书)"
    echo -e "  ${GREEN}4${NC}. 证书软链接管理 (创建和验证软链接)"
    echo -e "  ${GREEN}5${NC}. 证书路径验证 (验证证书路径配置)"
    echo -e "  ${GREEN}6${NC}. 智能证书管理 (推荐：自动检测和管理)"
    echo -e "  ${GREEN}7${NC}. 强制重新申请 (谨慎使用：强制申请新证书)"
    echo ""
    echo -e "  ${RED}0${NC}. 返回主菜单"
    echo ""
    echo -e "${CYAN}================================================================${NC}"
}

# 显示系统维护菜单
show_maintenance_menu() {
    show_header
    echo -e "${WHITE}系统维护 - 备份、监控和健康检查${NC}"
    echo ""
    echo -e "  ${GREEN}1${NC}. 系统备份 (备份配置文件和数据)"
    echo -e "  ${GREEN}2${NC}. 健康检查 (检查所有服务状态)"
    echo -e "  ${GREEN}3${NC}. 日志查看 (查看系统和应用日志)"
    echo -e "  ${GREEN}4${NC}. 性能监控 (查看系统资源使用情况)"
    echo -e "  ${GREEN}5${NC}. 服务重启 (重启Matrix相关服务)"
    echo -e "  ${GREEN}6${NC}. 数据库维护 (SQLite数据库优化)"
    echo -e "  ${GREEN}7${NC}. 清理临时文件 (清理日志和临时文件)"
    echo ""
    echo -e "  ${RED}0${NC}. 返回主菜单"
    echo ""
    echo -e "${CYAN}================================================================${NC}"
}

# 输入验证函数
validate_input() {
    local input="$1"
    local min="$2"
    local max="$3"
    
    if [[ ! "$input" =~ ^[0-9]+$ ]]; then
        return 1
    fi
    
    if [[ "$input" -lt "$min" || "$input" -gt "$max" ]]; then
        return 1
    fi
    
    return 0
}

# 获取用户输入
get_user_choice() {
    local min="$1"
    local max="$2"
    local choice
    
    while true; do
        echo -n -e "${WHITE}请输入选项 (${min}-${max}, 0=返回/退出): ${NC}"
        read -r choice
        
        if validate_input "$choice" 0 "$max"; then
            echo "$choice"
            return 0
        else
            log_error "无效输入！请输入 ${min}-${max} 之间的数字，或输入 0 返回/退出。"
            echo ""
        fi
    done
}

# 确认操作
confirm_action() {
    local message="$1"
    local choice
    
    echo ""
    echo -e "${YELLOW}$message${NC}"
    echo -n -e "${WHITE}确认执行吗？(y/N): ${NC}"
    read -r choice
    
    case "$choice" in
        [Yy]|[Yy][Ee][Ss]|是|确认)
            return 0
            ;;
        *)
            log_info "操作已取消。"
            return 1
            ;;
    esac
}

# 暂停等待用户
pause_for_user() {
    echo ""
    echo -n -e "${WHITE}按任意键继续...${NC}"
    read -n 1 -s
    echo ""
}

# 执行完整部署
execute_full_deployment() {
    show_header
    echo -e "${WHITE}完整部署 - 一键部署Matrix服务器${NC}"
    echo ""
    
    if confirm_action "这将执行完整的Matrix Synapse部署流程，包括证书申请、服务配置和启动。"; then
        log_info "开始执行完整部署..."
        
        # 检查配置文件
        if [[ ! -f "$PROJECT_ROOT/config.env" ]]; then
            log_error "配置文件 config.env 不存在！"
            log_info "请先复制 config-example.env 为 config.env 并填写配置信息。"
            pause_for_user
            return 1
        fi
        
        # 执行部署脚本
        cd "$PROJECT_ROOT"
        if ./deploy.sh; then
            log_success "Matrix Synapse部署完成！"
        else
            log_error "部署过程中出现错误，请检查日志。"
        fi
        
        pause_for_user
    fi
}

# 证书管理主函数
handle_certificate_management() {
    while true; do
        show_certificate_menu
        local choice
        choice=$(get_user_choice 1 7)
        
        case "$choice" in
            0)
                return 0
                ;;
            1)
                execute_certificate_apply
                ;;
            2)
                execute_certificate_status
                ;;
            3)
                execute_certificate_renew
                ;;
            4)
                execute_certificate_symlinks
                ;;
            5)
                execute_certificate_verify_paths
                ;;
            6)
                execute_certificate_smart
                ;;
            7)
                execute_certificate_force
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 主程序循环
main() {
    # 检查运行环境
    if [[ $EUID -eq 0 ]]; then
        log_warn "检测到以root用户运行，建议使用sudo执行特定操作。"
    fi
    
    # 检查项目目录
    if [[ ! -d "$PROJECT_ROOT" ]]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 主菜单循环
    while true; do
        show_main_menu
        local choice
        choice=$(get_user_choice 1 8)
        
        case "$choice" in
            0)
                echo ""
                log_info "感谢使用 Matrix Synapse 自动部署系统！"
                exit 0
                ;;
            1)
                execute_full_deployment
                ;;
            2)
                handle_certificate_management
                ;;
            3)
                handle_system_maintenance
                ;;
            4)
                handle_configuration_management
                ;;
            5)
                handle_user_management
                ;;
            6)
                handle_troubleshooting
                ;;
            7)
                show_system_information
                ;;
            8)
                show_help_documentation
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 证书管理主函数
handle_certificate_management() {
    while true; do
        show_certificate_menu
        local choice
        choice=$(get_user_choice 1 7)

        case "$choice" in
            0)
                return 0
                ;;
            1)
                execute_certificate_apply
                ;;
            2)
                execute_certificate_status
                ;;
            3)
                execute_certificate_renew
                ;;
            4)
                execute_certificate_symlinks
                ;;
            5)
                execute_certificate_verify_paths
                ;;
            6)
                execute_certificate_smart
                ;;
            7)
                execute_certificate_force
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 执行首次证书申请
execute_certificate_apply() {
    show_header
    echo -e "${WHITE}首次证书申请 - 申请新的SSL证书${NC}"
    echo ""

    if ! check_config_file || ! load_config; then
        pause_for_user
        return 1
    fi

    echo -e "${YELLOW}将为以下域名申请SSL证书：${NC}"
    echo -e "  - 基础域名: ${GREEN}${BASE_DOMAIN:-未配置}${NC}"
    echo -e "  - Matrix域名: ${GREEN}${MATRIX_DOMAIN:-未配置}${NC}"
    echo -e "  - TURN域名: ${GREEN}${TURN_DOMAIN:-未配置}${NC}"
    echo ""

    if confirm_action "确认申请这些域名的SSL证书吗？"; then
        log_info "开始申请SSL证书..."

        cd "$PROJECT_ROOT"
        if ./scripts/certificate-manager.sh smart "${BASE_DOMAIN}" "${MATRIX_DOMAIN}" "${TURN_DOMAIN}"; then
            log_success "SSL证书申请完成！"
        else
            log_error "证书申请过程中出现错误，请检查日志。"
        fi

        pause_for_user
    fi
}

# 执行证书状态检查
execute_certificate_status() {
    show_header
    echo -e "${WHITE}证书状态检查 - 查看证书有效期和状态${NC}"
    echo ""

    if ! check_config_file || ! load_config; then
        pause_for_user
        return 1
    fi

    log_info "检查证书状态..."

    cd "$PROJECT_ROOT"
    ./scripts/certificate-manager.sh status "${BASE_DOMAIN}" "${MATRIX_DOMAIN}" "${TURN_DOMAIN}"

    pause_for_user
}

# 执行证书手动续期
execute_certificate_renew() {
    show_header
    echo -e "${WHITE}证书手动续期 - 手动续期即将过期的证书${NC}"
    echo ""

    if confirm_action "确认手动续期即将过期的证书吗？"; then
        log_info "开始证书续期..."

        cd "$PROJECT_ROOT"
        if ./scripts/certificate-manager.sh renew; then
            log_success "证书续期完成！"
        else
            log_error "证书续期过程中出现错误，请检查日志。"
        fi

        pause_for_user
    fi
}

# 执行证书软链接管理
execute_certificate_symlinks() {
    show_header
    echo -e "${WHITE}证书软链接管理 - 创建和验证软链接${NC}"
    echo ""

    if ! check_config_file || ! load_config; then
        pause_for_user
        return 1
    fi

    if confirm_action "确认创建证书软链接吗？"; then
        log_info "开始创建证书软链接..."

        cd "$PROJECT_ROOT"
        if ./scripts/certificate-manager.sh create-symlinks "${BASE_DOMAIN}" "${MATRIX_DOMAIN}" "${TURN_DOMAIN}"; then
            log_success "证书软链接创建完成！"
        else
            log_error "证书软链接创建过程中出现错误，请检查日志。"
        fi

        pause_for_user
    fi
}

# 执行证书路径验证
execute_certificate_verify_paths() {
    show_header
    echo -e "${WHITE}证书路径验证 - 验证证书路径配置${NC}"
    echo ""

    log_info "验证证书路径配置..."

    cd "$PROJECT_ROOT"
    ./scripts/certificate-manager.sh verify-paths

    pause_for_user
}

# 执行智能证书管理
execute_certificate_smart() {
    show_header
    echo -e "${WHITE}智能证书管理 - 自动检测和管理证书${NC}"
    echo ""

    if ! check_config_file || ! load_config; then
        pause_for_user
        return 1
    fi

    echo -e "${GREEN}智能证书管理将自动：${NC}"
    echo -e "  - 检测现有证书状态"
    echo -e "  - 复用有效的现有证书"
    echo -e "  - 仅在必要时申请新证书"
    echo -e "  - 创建证书软链接"
    echo ""

    if confirm_action "确认执行智能证书管理吗？"; then
        log_info "开始智能证书管理..."

        cd "$PROJECT_ROOT"
        if ./scripts/certificate-manager.sh smart "${BASE_DOMAIN}" "${MATRIX_DOMAIN}" "${TURN_DOMAIN}"; then
            log_success "智能证书管理完成！"
        else
            log_error "智能证书管理过程中出现错误，请检查日志。"
        fi

        pause_for_user
    fi
}

# 执行强制重新申请
execute_certificate_force() {
    show_header
    echo -e "${WHITE}强制重新申请 - 强制申请新证书${NC}"
    echo ""

    if ! check_config_file || ! load_config; then
        pause_for_user
        return 1
    fi

    echo -e "${RED}警告：强制重新申请将忽略现有证书，可能浪费Let's Encrypt配额！${NC}"
    echo -e "${YELLOW}建议仅在证书损坏或配置错误时使用此功能。${NC}"
    echo ""

    if confirm_action "确认强制重新申请所有证书吗？"; then
        if confirm_action "再次确认：这将强制申请新证书，可能浪费配额！"; then
            log_info "开始强制重新申请证书..."

            cd "$PROJECT_ROOT"
            if ./scripts/certificate-manager.sh force "${BASE_DOMAIN}" "${MATRIX_DOMAIN}" "${TURN_DOMAIN}"; then
                log_success "强制证书申请完成！"
            else
                log_error "强制证书申请过程中出现错误，请检查日志。"
            fi

            pause_for_user
        fi
    fi
}

# 系统维护主函数
handle_system_maintenance() {
    while true; do
        show_maintenance_menu
        local choice
        choice=$(get_user_choice 1 7)

        case "$choice" in
            0)
                return 0
                ;;
            1)
                execute_system_backup
                ;;
            2)
                execute_health_check
                ;;
            3)
                execute_log_view
                ;;
            4)
                execute_performance_monitor
                ;;
            5)
                execute_service_restart
                ;;
            6)
                execute_database_maintenance
                ;;
            7)
                execute_cleanup_temp_files
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 配置管理主函数
handle_configuration_management() {
    while true; do
        show_config_menu
        local choice
        choice=$(get_user_choice 1 5)

        case "$choice" in
            0)
                return 0
                ;;
            1)
                show_current_config
                ;;
            2)
                edit_config_file
                ;;
            3)
                validate_config_file
                ;;
            4)
                reset_config_file
                ;;
            5)
                update_config_templates
                ;;
            *)
                log_error "无效选项，请重新选择。"
                pause_for_user
                ;;
        esac
    done
}

# 占位函数（待实现）
handle_user_management() {
    log_info "用户管理功能正在开发中..."
    pause_for_user
}

handle_troubleshooting() {
    log_info "故障排除功能正在开发中..."
    pause_for_user
}

show_system_information() {
    log_info "系统信息功能正在开发中..."
    pause_for_user
}

show_help_documentation() {
    log_info "帮助文档功能正在开发中..."
    pause_for_user
}

execute_system_backup() {
    log_info "系统备份功能正在开发中..."
    pause_for_user
}

execute_health_check() {
    log_info "健康检查功能正在开发中..."
    pause_for_user
}

execute_log_view() {
    log_info "日志查看功能正在开发中..."
    pause_for_user
}

execute_performance_monitor() {
    log_info "性能监控功能正在开发中..."
    pause_for_user
}

execute_service_restart() {
    log_info "服务重启功能正在开发中..."
    pause_for_user
}

execute_database_maintenance() {
    log_info "数据库维护功能正在开发中..."
    pause_for_user
}

execute_cleanup_temp_files() {
    log_info "清理临时文件功能正在开发中..."
    pause_for_user
}

show_current_config() {
    log_info "查看当前配置功能正在开发中..."
    pause_for_user
}

edit_config_file() {
    log_info "编辑配置文件功能正在开发中..."
    pause_for_user
}

validate_config_file() {
    log_info "验证配置文件功能正在开发中..."
    pause_for_user
}

reset_config_file() {
    log_info "重置配置文件功能正在开发中..."
    pause_for_user
}

update_config_templates() {
    log_info "更新配置模板功能正在开发中..."
    pause_for_user
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
