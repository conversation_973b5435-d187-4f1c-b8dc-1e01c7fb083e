#!/bin/bash

# Matrix Synapse 智能证书管理脚本
# 使用acme.sh和Cloudflare DNS API自动管理SSL证书
# 支持证书申请、续期、部署和监控
# 包含Let's Encrypt配额保护机制

# 版本: 2.0.0
# 日期: 2025-07-13

set -e

# 配置变量
INSTALL_PATH="${INSTALL_PATH:-/opt/matrix}"
DOMAIN_NAME="${DOMAIN_NAME:-}"
CF_TOKEN="${CF_TOKEN:-}"
CERT_EMAIL="${CERT_EMAIL:-}"
LOG_FILE="$INSTALL_PATH/logs/certificate.log"

# acme.sh配置
ACME_HOME="${ACME_HOME:-/root/.acme.sh}"
CERT_PATH="$INSTALL_PATH/certs"

# 证书有效期配置
CERT_RENEWAL_DAYS=30  # 证书剩余天数少于30天时续期
CERT_MIN_VALID_DAYS=7 # 证书剩余天数少于7天时强制重新申请

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warn() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# 检查证书有效期
check_certificate_validity() {
    local domain="$1"
    local cert_file="$2"

    if [[ ! -f "$cert_file" ]]; then
        log_info "证书文件不存在: $cert_file"
        return 1
    fi

    # 获取证书过期时间
    local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
    local expiry_timestamp=$(date -d "$expiry_date" +%s)
    local current_timestamp=$(date +%s)
    local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))

    log_info "域名 $domain 证书剩余有效期: $days_until_expiry 天"

    if [[ $days_until_expiry -lt $CERT_MIN_VALID_DAYS ]]; then
        log_warn "证书即将过期 (少于 $CERT_MIN_VALID_DAYS 天)，需要重新申请"
        return 1
    elif [[ $days_until_expiry -lt $CERT_RENEWAL_DAYS ]]; then
        log_warn "证书需要续期 (少于 $CERT_RENEWAL_DAYS 天)"
        return 2
    else
        log_info "证书有效期充足，无需操作"
        return 0
    fi
}

# 检查是否需要申请证书
need_certificate() {
    local domain="$1"
    local cert_dir="$2"
    local cert_file="$cert_dir/fullchain.pem"

    # 检查证书文件是否存在
    if [[ ! -f "$cert_file" ]]; then
        log_info "域名 $domain 证书不存在，需要申请"
        return 0
    fi

    # 检查证书有效期
    check_certificate_validity "$domain" "$cert_file"
    local validity_status=$?

    case $validity_status in
        0)
            log_info "域名 $domain 证书有效，跳过申请"
            return 1
            ;;
        1)
            log_warn "域名 $domain 证书无效或即将过期，需要重新申请"
            return 0
            ;;
        2)
            log_warn "域名 $domain 证书需要续期"
            return 2
            ;;
    esac
}

# 安全的证书申请函数
safe_issue_certificate() {
    local domain="$1"
    local force_renew="${2:-false}"

    log_info "开始为域名 $domain 申请证书..."

    # 检查acme.sh是否已安装
    if [[ ! -f "$ACME_HOME/acme.sh" ]]; then
        log_error "acme.sh未安装，请先安装acme.sh"
        return 1
    fi

    # 设置Cloudflare API Token
    export CF_Token="$CF_TOKEN"

    # 构建acme.sh命令
    local acme_cmd="$ACME_HOME/acme.sh --issue --dns dns_cf -d $domain"

    if [[ "$force_renew" == "true" ]]; then
        acme_cmd="$acme_cmd --force"
        log_warn "强制重新申请证书: $domain"
    fi

    # 执行证书申请
    if eval "$acme_cmd"; then
        log_info "证书申请成功: $domain"
        return 0
    else
        log_error "证书申请失败: $domain"
        return 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查acme.sh
    if [[ ! -f "$ACME_HOME/acme.sh" ]]; then
        log_error "acme.sh未安装，请先运行部署脚本"
        exit 1
    fi
    
    # 检查必要参数
    if [[ -z "$DOMAIN_NAME" ]]; then
        log_error "未设置域名，请设置DOMAIN_NAME环境变量"
        exit 1
    fi
    
    if [[ -z "$CF_TOKEN" ]]; then
        log_error "未设置Cloudflare API Token，请设置CF_TOKEN环境变量"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl未安装"
        exit 1
    fi
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    export CF_Token="$CF_TOKEN"
    export CERT_HOME="$CERT_PATH"
    
    # 创建证书目录
    mkdir -p "$CERT_PATH"
    
    log_info "环境变量设置完成"
}

# 智能证书管理 - 为单个域名申请或续期证书
manage_domain_certificate() {
    local domain="$1"
    local cert_subdir="$2"
    local force_renew="${3:-false}"

    log_info "管理域名 $domain 的证书..."

    local cert_dir="$CERT_PATH/$cert_subdir"
    mkdir -p "$cert_dir"

    # 检查是否需要申请证书
    if [[ "$force_renew" == "true" ]]; then
        log_info "强制重新申请证书: $domain"
        need_cert=true
    else
        need_certificate "$domain" "$cert_dir"
        local cert_status=$?

        case $cert_status in
            0)
                need_cert=true
                ;;
            1)
                log_info "域名 $domain 证书有效，跳过申请"
                return 0
                ;;
            2)
                log_info "域名 $domain 证书需要续期"
                need_cert=true
                ;;
        esac
    fi

    if [[ "$need_cert" == "true" ]]; then
        # 申请证书
        if safe_issue_certificate "$domain" "$force_renew"; then
            # 安装证书
            install_domain_certificate "$domain" "$cert_dir"
        else
            log_error "域名 $domain 证书申请失败"
            return 1
        fi
    fi
}

# 安装域名证书
install_domain_certificate() {
    local domain="$1"
    local cert_dir="$2"

    log_info "安装域名 $domain 的证书到 $cert_dir"

    if $ACME_HOME/acme.sh --install-cert -d "$domain" \
        --cert-file "$cert_dir/cert.pem" \
        --key-file "$cert_dir/key.pem" \
        --fullchain-file "$cert_dir/fullchain.pem" \
        --ca-file "$cert_dir/ca.pem"; then

        # 设置证书权限
        chmod 644 "$cert_dir"/*.pem

        log_info "域名 $domain 证书安装成功"
        return 0
    else
        log_error "域名 $domain 证书安装失败"
        return 1
    fi
}

# 智能证书申请 - 管理所有域名证书
smart_certificate_management() {
    local base_domain="$1"
    local matrix_domain="$2"
    local turn_domain="$3"
    local force_renew="${4:-false}"

    log_info "开始智能证书管理..."
    log_info "基础域名: $base_domain"
    log_info "Matrix域名: $matrix_domain"
    log_info "TURN域名: $turn_domain"

    local success_count=0
    local total_count=3

    # 管理基础域名证书
    if manage_domain_certificate "$base_domain" "base" "$force_renew"; then
        ((success_count++))
    fi

    # 管理Matrix域名证书
    if manage_domain_certificate "$matrix_domain" "matrix" "$force_renew"; then
        ((success_count++))
    fi

    # 管理TURN域名证书
    if manage_domain_certificate "$turn_domain" "turn" "$force_renew"; then
        ((success_count++))
    fi

    log_info "证书管理完成: $success_count/$total_count 成功"

    if [[ $success_count -eq $total_count ]]; then
        log_info "所有证书管理成功"
        return 0
    else
        log_error "部分证书管理失败"
        return 1
    fi
}

# 检查所有证书状态
check_all_certificates() {
    local base_domain="$1"
    local matrix_domain="$2"
    local turn_domain="$3"

    log_info "检查所有证书状态..."

    echo "=================================="
    echo "证书状态报告"
    echo "=================================="

    # 检查基础域名证书
    echo "基础域名: $base_domain"
    local base_cert="$CERT_PATH/base/fullchain.pem"
    if [[ -f "$base_cert" ]]; then
        check_certificate_validity "$base_domain" "$base_cert"
        local status=$?
        case $status in
            0) echo "状态: ✓ 有效" ;;
            1) echo "状态: ✗ 无效或即将过期" ;;
            2) echo "状态: ⚠ 需要续期" ;;
        esac
    else
        echo "状态: ✗ 证书不存在"
    fi
    echo ""

    # 检查Matrix域名证书
    echo "Matrix域名: $matrix_domain"
    local matrix_cert="$CERT_PATH/matrix/fullchain.pem"
    if [[ -f "$matrix_cert" ]]; then
        check_certificate_validity "$matrix_domain" "$matrix_cert"
        local status=$?
        case $status in
            0) echo "状态: ✓ 有效" ;;
            1) echo "状态: ✗ 无效或即将过期" ;;
            2) echo "状态: ⚠ 需要续期" ;;
        esac
    else
        echo "状态: ✗ 证书不存在"
    fi
    echo ""

    # 检查TURN域名证书
    echo "TURN域名: $turn_domain"
    local turn_cert="$CERT_PATH/turn/fullchain.pem"
    if [[ -f "$turn_cert" ]]; then
        check_certificate_validity "$turn_domain" "$turn_cert"
        local status=$?
        case $status in
            0) echo "状态: ✓ 有效" ;;
            1) echo "状态: ✗ 无效或即将过期" ;;
            2) echo "状态: ⚠ 需要续期" ;;
        esac
    else
        echo "状态: ✗ 证书不存在"
    fi
    echo "=================================="
}

# 重新加载服务
reload_services() {
    log_info "重新加载相关服务..."

    # 尝试重新加载nginx
    if systemctl is-active --quiet nginx; then
        systemctl reload nginx
        log_info "Nginx重新加载成功"
    elif [[ -f "$INSTALL_PATH/docker-compose.yml" ]]; then
        cd "$INSTALL_PATH"
        docker compose restart nginx
        log_info "Docker Nginx重新启动成功"
    else
        log_warn "未找到Nginx服务"
    fi
}

# 显示使用说明
show_usage() {
    echo "Matrix Synapse 智能证书管理脚本 v2.0.0"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  smart <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 智能管理所有域名证书 (推荐)"
    echo "  status <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 检查所有证书状态"
    echo "  force <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 强制重新申请所有证书"
    echo "  renew             - 自动续期即将过期的证书"
    echo "  help              - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  CF_TOKEN          - Cloudflare API Token (必需)"
    echo "  INSTALL_PATH      - Matrix安装路径 (默认: /opt/matrix)"
    echo "  CERT_EMAIL        - 证书邮箱地址"
    echo ""
    echo "示例:"
    echo "  # 智能管理证书 (检查有效期，仅在需要时申请)"
    echo "  $0 smart example.com matrix.example.com turn.example.com"
    echo ""
    echo "  # 检查证书状态"
    echo "  $0 status example.com matrix.example.com turn.example.com"
    echo ""
    echo "  # 强制重新申请所有证书"
    echo "  $0 force example.com matrix.example.com turn.example.com"
    echo ""
    echo "  # 自动续期 (通常用于cron任务)"
    echo "  $0 renew"
    echo ""
    echo "Let's Encrypt 配额保护:"
    echo "  • 每周最多5次重复证书申请"
    echo "  • 智能模式会检查证书有效期，避免不必要的申请"
    echo "  • 证书剩余有效期超过30天时不会重新申请"
    echo "  • 证书剩余有效期少于7天时会强制重新申请"
}

# 自动续期所有证书
auto_renew_all() {
    log_info "执行自动续期检查..."

    # 使用acme.sh的自动续期功能
    if "$ACME_HOME/acme.sh" --cron --home "$ACME_HOME"; then
        log_info "自动续期检查完成"
        reload_services
        return 0
    else
        log_error "自动续期检查失败"
        return 1
    fi
}

# 主函数
main() {
    local command="${1:-help}"

    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"

    case "$command" in
        "smart")
            if [[ $# -lt 4 ]]; then
                log_error "smart命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            check_dependencies
            setup_environment
            smart_certificate_management "$base_domain" "$matrix_domain" "$turn_domain" false
            reload_services
            ;;

        "status")
            if [[ $# -lt 4 ]]; then
                log_error "status命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            check_all_certificates "$base_domain" "$matrix_domain" "$turn_domain"
            ;;

        "force")
            if [[ $# -lt 4 ]]; then
                log_error "force命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            log_warn "强制重新申请所有证书，这将消耗Let's Encrypt配额"
            read -p "确认继续? (y/N): " confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                check_dependencies
                setup_environment
                smart_certificate_management "$base_domain" "$matrix_domain" "$turn_domain" true
                reload_services
            else
                log_info "操作已取消"
            fi
            ;;

        "renew")
            check_dependencies
            setup_environment
            auto_renew_all
            ;;

        "help"|"-h"|"--help")
            show_usage
            ;;

        *)
            log_error "未知命令: $command"
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi






