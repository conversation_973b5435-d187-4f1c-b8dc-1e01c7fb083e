#!/bin/bash

# Matrix Synapse 智能证书管理脚本
# 使用acme.sh和Cloudflare DNS API自动管理SSL证书
# 支持证书申请、续期、部署和监控
# 包含Let's Encrypt配额保护机制

# 版本: 2.0.0
# 日期: 2025-07-13

set -e

# 配置变量
INSTALL_PATH="${INSTALL_PATH:-/opt/matrix}"
DOMAIN_NAME="${DOMAIN_NAME:-}"
CF_TOKEN="${CF_TOKEN:-}"
CERT_EMAIL="${CERT_EMAIL:-}"
LOG_FILE="$INSTALL_PATH/logs/certificate.log"

# 重复申请保护配置
CERT_VALIDITY_THRESHOLD="${CERT_VALIDITY_THRESHOLD:-30}"  # 证书有效期阈值（天）
PROTECTION_LOG_FILE="$INSTALL_PATH/logs/certificate-protection.log"
CERT_PATH="$INSTALL_PATH/certs"

# acme.sh配置
# 自动检测acme.sh安装路径
if [[ -z "$ACME_HOME" ]]; then
    if [[ -d "$HOME/.acme.sh" ]]; then
        ACME_HOME="$HOME/.acme.sh"
    elif [[ -d "/root/.acme.sh" ]]; then
        ACME_HOME="/root/.acme.sh"
    else
        ACME_HOME="/root/.acme.sh"  # 默认路径
    fi
fi
CERT_PATH="$INSTALL_PATH/certs"

# 证书有效期配置
CERT_RENEWAL_DAYS=30  # 证书剩余天数少于30天时续期
CERT_MIN_VALID_DAYS=7 # 证书剩余天数少于7天时强制重新申请

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warn() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# 检查证书有效期
check_certificate_validity() {
    local domain="$1"
    local cert_file="$2"

    if [[ ! -f "$cert_file" ]]; then
        log_info "证书文件不存在: $cert_file"
        return 1
    fi

    # 获取证书过期时间
    local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)

    # 处理不同的日期格式
    local expiry_timestamp
    if command -v gdate &> /dev/null; then
        # macOS with GNU date
        expiry_timestamp=$(gdate -d "$expiry_date" +%s 2>/dev/null)
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS native date (limited functionality)
        expiry_timestamp=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s 2>/dev/null)
    else
        # Linux date
        expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null)
    fi

    if [[ -z "$expiry_timestamp" ]]; then
        log_error "无法解析证书过期时间: $expiry_date"
        return 1
    fi

    local current_timestamp=$(date +%s)
    local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))

    log_info "域名 $domain 证书过期时间: $expiry_date"
    log_info "域名 $domain 证书剩余有效期: $days_until_expiry 天"

    if [[ $days_until_expiry -lt $CERT_MIN_VALID_DAYS ]]; then
        log_warn "证书即将过期 (少于 $CERT_MIN_VALID_DAYS 天)，需要重新申请"
        return 1
    elif [[ $days_until_expiry -lt $CERT_RENEWAL_DAYS ]]; then
        log_warn "证书需要续期 (少于 $CERT_RENEWAL_DAYS 天)"
        return 2
    else
        log_info "证书有效期充足 ($days_until_expiry 天)，无需操作"
        return 0
    fi
}

# 检查acme.sh中的证书状态
check_acme_certificate_status() {
    local domain="$1"

    log_info "检查acme.sh中域名 $domain 的证书状态..."

    # 检查acme.sh是否管理此域名
    if [[ -f "$ACME_HOME/acme.sh" ]]; then
        local acme_info=$("$ACME_HOME/acme.sh" --list | grep "$domain" 2>/dev/null)
        if [[ -n "$acme_info" ]]; then
            log_info "acme.sh中找到域名 $domain 的证书记录"
            log_info "证书信息: $acme_info"

            # 检查续期状态
            local renew_info=$("$ACME_HOME/acme.sh" --cron --home "$ACME_HOME" --domain "$domain" --dry-run 2>&1)
            if echo "$renew_info" | grep -q "Skip"; then
                log_info "acme.sh报告域名 $domain 证书无需续期"
                return 0  # 证书有效，无需操作
            fi
        else
            log_info "acme.sh中未找到域名 $domain 的证书记录"
        fi
    fi

    return 1  # 需要进一步检查
}

# 检查是否需要申请证书
need_certificate() {
    local domain="$1"
    local cert_dir="$2"
    local cert_file="$cert_dir/fullchain.pem"

    # 首先检查acme.sh的证书状态
    if check_acme_certificate_status "$domain"; then
        log_info "域名 $domain 在acme.sh中状态良好，跳过申请"
        return 1
    fi

    # 检查证书文件是否存在
    if [[ ! -f "$cert_file" ]]; then
        log_info "域名 $domain 证书文件不存在，需要申请"
        return 0
    fi

    # 检查证书有效期
    check_certificate_validity "$domain" "$cert_file"
    local validity_status=$?

    case $validity_status in
        0)
            log_info "域名 $domain 证书有效，跳过申请"
            return 1
            ;;
        1)
            log_warn "域名 $domain 证书无效或即将过期，需要重新申请"
            return 0
            ;;
        2)
            log_warn "域名 $domain 证书需要续期"
            return 2
            ;;
    esac
}

# 安全的证书申请函数
safe_issue_certificate() {
    local domain="$1"
    local force_renew="${2:-false}"

    log_info "开始为域名 $domain 申请证书..."

    # 检查acme.sh是否已安装
    if [[ ! -f "$ACME_HOME/acme.sh" ]]; then
        log_error "acme.sh未安装，请先安装acme.sh"
        return 1
    fi

    # 设置Cloudflare API Token
    export CF_Token="$CF_TOKEN"

    # 构建acme.sh命令
    local acme_cmd="$ACME_HOME/acme.sh --issue --dns dns_cf -d $domain"

    if [[ "$force_renew" == "true" ]]; then
        acme_cmd="$acme_cmd --force"
        log_warn "强制重新申请证书: $domain"
    fi

    # 执行证书申请并捕获输出
    local acme_output
    acme_output=$(eval "$acme_cmd" 2>&1)
    local acme_result=$?

    # 检查输出内容
    if [[ $acme_result -eq 0 ]]; then
        log_info "证书申请成功: $domain"
        return 0
    elif echo "$acme_output" | grep -q "Domains not changed\|Skipping\|Next renewal time"; then
        log_info "证书无需续期: $domain"
        log_info "acme.sh输出: $(echo "$acme_output" | grep -E "Skipping|Next renewal time" | head -1)"
        return 2  # 返回2表示跳过
    else
        log_error "证书申请失败: $domain"
        log_error "acme.sh输出: $acme_output"
        return 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."

    # 检查并验证acme.sh路径
    log_info "检查acme.sh安装路径: $ACME_HOME"
    if [[ ! -f "$ACME_HOME/acme.sh" ]]; then
        # 尝试其他可能的路径
        local alt_paths=("$HOME/.acme.sh" "/root/.acme.sh" "/usr/local/share/acme.sh")
        local found=false

        for path in "${alt_paths[@]}"; do
            if [[ -f "$path/acme.sh" ]]; then
                log_info "在 $path 找到acme.sh，更新ACME_HOME"
                ACME_HOME="$path"
                found=true
                break
            fi
        done

        if [[ "$found" == "false" ]]; then
            log_error "acme.sh未安装，请先运行部署脚本"
            log_error "检查的路径: $ACME_HOME ${alt_paths[*]}"
            exit 1
        fi
    fi

    log_info "acme.sh路径验证成功: $ACME_HOME"

    # 检查CF_TOKEN (仅在需要申请证书时检查)
    if [[ -z "$CF_TOKEN" ]] && [[ "$1" != "status" ]]; then
        log_warn "未设置Cloudflare API Token，某些操作可能失败"
    fi

    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl未安装"
        exit 1
    fi

    log_info "依赖检查完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    export CF_Token="$CF_TOKEN"
    export CERT_HOME="$CERT_PATH"
    
    # 创建证书目录
    mkdir -p "$CERT_PATH"
    
    log_info "环境变量设置完成"
}

# 智能证书管理 - 为单个域名申请或续期证书
manage_domain_certificate() {
    local domain="$1"
    local cert_subdir="$2"
    local force_renew="${3:-false}"

    log_info "管理域名 $domain 的证书..."

    local cert_dir="$CERT_PATH/$cert_subdir"
    mkdir -p "$cert_dir"

    # 检查是否需要申请证书
    local need_cert=false

    if [[ "$force_renew" == "true" ]]; then
        log_info "强制重新申请证书: $domain"
        need_cert=true
    else
        need_certificate "$domain" "$cert_dir"
        local cert_status=$?

        case $cert_status in
            0)
                log_info "域名 $domain 需要申请证书"
                need_cert=true
                ;;
            1)
                log_info "域名 $domain 证书有效，跳过申请"
                # 确保证书文件在正确位置（优先使用软链接）
                if ensure_certificate_installed "$domain" "$cert_dir" true; then
                    log_info "域名 $domain 证书确认已安装（软链接模式）"
                    return 2  # 返回2表示跳过（证书有效）
                else
                    log_info "域名 $domain 证书安装完成"
                    return 0  # 安装成功
                fi
                ;;
            2)
                log_info "域名 $domain 证书需要续期"
                need_cert=true
                ;;
        esac
    fi

    if [[ "$need_cert" == "true" ]]; then
        # 申请或续期证书
        safe_issue_certificate "$domain" "$force_renew"
        local issue_result=$?

        case $issue_result in
            0)
                # 证书申请成功，安装证书
                if install_domain_certificate "$domain" "$cert_dir"; then
                    return 0  # 成功
                else
                    return 1  # 安装失败
                fi
                ;;
            2)
                # 证书无需续期，确保已安装
                log_info "域名 $domain 证书无需续期，确保已安装（软链接模式）"
                if ensure_certificate_installed "$domain" "$cert_dir" true; then
                    return 2  # 跳过（证书有效）
                else
                    return 0  # 安装成功
                fi
                ;;
            *)
                # 证书申请失败
                log_error "域名 $domain 证书申请失败"
                # 尝试从acme.sh安装现有证书（优先软链接）
                if ensure_certificate_installed "$domain" "$cert_dir" true; then
                    log_info "从acme.sh安装现有证书成功（软链接模式）"
                    return 2  # 跳过（使用现有证书）
                else
                    return 1  # 失败
                fi
                ;;
        esac
    fi

    return 0  # 默认成功
}

# 确保证书已安装到指定目录（支持软链接）
ensure_certificate_installed() {
    local domain="$1"
    local cert_dir="$2"
    local prefer_symlink="${3:-true}"

    log_info "确保域名 $domain 的证书已安装到 $cert_dir"

    # 检查目标目录是否已有有效证书
    if verify_certificate_symlinks "$domain" "$cert_dir"; then
        log_info "证书文件已存在且有效于目标目录"
        return 0
    fi

    # 尝试从acme.sh安装证书（优先软链接）
    if [[ -f "$ACME_HOME/acme.sh" ]]; then
        log_info "尝试从acme.sh安装域名 $domain 的证书"
        if install_domain_certificate "$domain" "$cert_dir" "$prefer_symlink"; then
            log_info "从acme.sh安装证书成功"
            return 0
        fi
    fi

    log_warn "无法确保证书安装"
    return 1
}

# 查找acme.sh中的证书文件路径
find_acme_certificate_path() {
    local domain="$1"

    log_info "查找域名 $domain 在acme.sh中的证书路径..."

    # acme.sh证书存储的可能路径
    local possible_paths=(
        "$ACME_HOME/$domain"
        "$ACME_HOME/${domain}_ecc"
        "$ACME_HOME/$domain/$domain.cer"
    )

    # 查找证书目录
    for path in "${possible_paths[@]}"; do
        if [[ -d "$(dirname "$path")" ]]; then
            local cert_dir="$(dirname "$path")"
            if [[ -f "$cert_dir/fullchain.cer" && -f "$cert_dir/$domain.key" ]]; then
                log_info "找到证书目录: $cert_dir"
                echo "$cert_dir"
                return 0
            fi
        fi
    done

    # 使用acme.sh --list查找
    if [[ -f "$ACME_HOME/acme.sh" ]]; then
        local cert_info=$("$ACME_HOME/acme.sh" --list | grep "$domain" | head -1)
        if [[ -n "$cert_info" ]]; then
            # 从acme.sh --list输出中提取路径
            local cert_path=$(echo "$cert_info" | awk '{print $NF}')
            if [[ -f "$cert_path" ]]; then
                local cert_dir="$(dirname "$cert_path")"
                log_info "通过acme.sh --list找到证书目录: $cert_dir"
                echo "$cert_dir"
                return 0
            fi
        fi
    fi

    log_warn "未找到域名 $domain 的证书路径"
    return 1
}

# 创建证书软链接
create_certificate_symlinks() {
    local domain="$1"
    local cert_dir="$2"
    local acme_cert_dir="$3"

    log_info "为域名 $domain 创建证书软链接..."
    log_info "源目录: $acme_cert_dir"
    log_info "目标目录: $cert_dir"

    # 确保目标目录存在
    mkdir -p "$cert_dir"

    # 定义证书文件映射
    declare -A cert_files=(
        ["fullchain.pem"]="fullchain.cer"
        ["cert.pem"]="$domain.cer"
        ["key.pem"]="$domain.key"
        ["ca.pem"]="ca.cer"
    )

    local success_count=0
    local total_count=${#cert_files[@]}

    # 创建软链接
    for target_file in "${!cert_files[@]}"; do
        local source_file="${cert_files[$target_file]}"
        local source_path="$acme_cert_dir/$source_file"
        local target_path="$cert_dir/$target_file"

        # 检查源文件是否存在
        if [[ ! -f "$source_path" ]]; then
            log_warn "源证书文件不存在: $source_path"
            continue
        fi

        # 删除现有的目标文件或链接
        if [[ -e "$target_path" || -L "$target_path" ]]; then
            rm -f "$target_path"
        fi

        # 创建软链接
        if ln -s "$source_path" "$target_path" 2>/dev/null; then
            log_info "✓ 创建软链接: $target_file -> $source_file"
            ((success_count++))
        else
            log_warn "✗ 软链接创建失败: $target_file"
        fi
    done

    log_info "软链接创建完成: $success_count/$total_count 成功"

    # 验证软链接
    if [[ $success_count -ge 2 ]]; then  # 至少需要fullchain.pem和key.pem
        if [[ -f "$cert_dir/fullchain.pem" && -f "$cert_dir/key.pem" ]]; then
            log_info "证书软链接验证成功"
            return 0
        fi
    fi

    log_error "证书软链接验证失败"
    return 1
}

# 验证软链接的有效性
verify_certificate_symlinks() {
    local domain="$1"
    local cert_dir="$2"

    log_info "验证域名 $domain 的证书软链接..."

    # 检查必需的证书文件
    local required_files=("fullchain.pem" "key.pem")
    local valid_count=0

    for file in "${required_files[@]}"; do
        local file_path="$cert_dir/$file"

        if [[ -L "$file_path" ]]; then
            # 检查软链接目标是否存在
            if [[ -f "$file_path" ]]; then
                local target=$(readlink "$file_path")
                log_info "✓ $file -> $target (软链接有效)"
                ((valid_count++))
            else
                log_warn "✗ $file 软链接目标不存在"
            fi
        elif [[ -f "$file_path" ]]; then
            log_info "✓ $file (常规文件)"
            ((valid_count++))
        else
            log_warn "✗ $file 不存在"
        fi
    done

    if [[ $valid_count -eq ${#required_files[@]} ]]; then
        log_info "证书软链接验证通过"
        return 0
    else
        log_error "证书软链接验证失败: $valid_count/${#required_files[@]} 有效"
        return 1
    fi
}

# 安装域名证书（增强版，支持软链接）
install_domain_certificate() {
    local domain="$1"
    local cert_dir="$2"
    local prefer_symlink="${3:-true}"  # 默认优先使用软链接

    log_info "安装域名 $domain 的证书到 $cert_dir (软链接模式: $prefer_symlink)"

    # 确保目标目录存在
    mkdir -p "$cert_dir"

    # 策略1: 优先尝试创建软链接
    if [[ "$prefer_symlink" == "true" ]]; then
        log_info "尝试创建证书软链接..."

        local acme_cert_dir
        acme_cert_dir=$(find_acme_certificate_path "$domain")

        if [[ $? -eq 0 && -n "$acme_cert_dir" ]]; then
            log_info "找到acme.sh证书目录: $acme_cert_dir"

            # 创建软链接
            if create_certificate_symlinks "$domain" "$cert_dir" "$acme_cert_dir"; then
                log_info "证书软链接创建成功"

                # 验证软链接
                if verify_certificate_symlinks "$domain" "$cert_dir"; then
                    log_info "域名 $domain 证书软链接安装成功"
                    return 0
                else
                    log_warn "证书软链接验证失败，尝试传统安装方式"
                fi
            else
                log_warn "证书软链接创建失败，尝试传统安装方式"
            fi
        else
            log_warn "未找到acme.sh证书目录，尝试传统安装方式"
        fi
    fi

    # 策略2: 传统的acme.sh --install-cert方式
    log_info "使用传统方式安装证书..."

    if "$ACME_HOME/acme.sh" --install-cert -d "$domain" \
        --cert-file "$cert_dir/cert.pem" \
        --key-file "$cert_dir/key.pem" \
        --fullchain-file "$cert_dir/fullchain.pem" \
        --ca-file "$cert_dir/ca.pem" 2>/dev/null; then

        # 设置证书权限
        chmod 644 "$cert_dir"/*.pem 2>/dev/null

        log_info "域名 $domain 证书传统安装成功"

        # 验证安装的证书
        if [[ -f "$cert_dir/fullchain.pem" && -f "$cert_dir/key.pem" ]]; then
            log_info "证书文件验证成功"
            return 0
        else
            log_warn "证书文件验证失败，但安装命令成功"
            return 1
        fi
    else
        log_warn "域名 $domain 证书传统安装失败"

        # 策略3: 检查是否已有有效证书文件
        if [[ -f "$cert_dir/fullchain.pem" && -f "$cert_dir/key.pem" ]]; then
            log_info "发现现有证书文件，验证有效性..."
            if verify_certificate_symlinks "$domain" "$cert_dir"; then
                log_info "现有证书有效，使用现有证书"
                return 0
            fi
        fi

        return 1
    fi
}

# 智能证书申请 - 管理所有域名证书
smart_certificate_management() {
    local base_domain="$1"
    local matrix_domain="$2"
    local turn_domain="$3"
    local force_renew="${4:-false}"

    log_info "开始智能证书管理..."
    log_info "基础域名: $base_domain"
    log_info "Matrix域名: $matrix_domain"
    log_info "TURN域名: $turn_domain"

    local success_count=0
    local skip_count=0
    local total_count=3

    # 管理基础域名证书
    manage_domain_certificate "$base_domain" "base" "$force_renew"
    local base_result=$?
    if [[ $base_result -eq 0 ]]; then
        ((success_count++))
    elif [[ $base_result -eq 2 ]]; then
        ((skip_count++))
        log_info "基础域名证书跳过（已有效）"
    fi

    # 管理Matrix域名证书
    manage_domain_certificate "$matrix_domain" "matrix" "$force_renew"
    local matrix_result=$?
    if [[ $matrix_result -eq 0 ]]; then
        ((success_count++))
    elif [[ $matrix_result -eq 2 ]]; then
        ((skip_count++))
        log_info "Matrix域名证书跳过（已有效）"
    fi

    # 管理TURN域名证书
    manage_domain_certificate "$turn_domain" "turn" "$force_renew"
    local turn_result=$?
    if [[ $turn_result -eq 0 ]]; then
        ((success_count++))
    elif [[ $turn_result -eq 2 ]]; then
        ((skip_count++))
        log_info "TURN域名证书跳过（已有效）"
    fi

    local handled_count=$((success_count + skip_count))
    log_info "证书管理完成: $success_count 成功, $skip_count 跳过, $handled_count/$total_count 处理"

    if [[ $handled_count -eq $total_count ]]; then
        log_info "所有证书管理成功（包括跳过的有效证书）"
        return 0
    else
        log_error "部分证书管理失败"
        return 1
    fi
}

# 检查所有证书状态
check_all_certificates() {
    local base_domain="$1"
    local matrix_domain="$2"
    local turn_domain="$3"

    log_info "检查所有证书状态..."

    echo "=================================="
    echo "证书状态报告 - $(date)"
    echo "=================================="

    # 检查acme.sh管理的证书
    echo "acme.sh管理的证书:"
    if [[ -f "$ACME_HOME/acme.sh" ]]; then
        "$ACME_HOME/acme.sh" --list 2>/dev/null | grep -E "($base_domain|$matrix_domain|$turn_domain)" || echo "  未找到相关证书记录"
    else
        echo "  acme.sh未安装"
    fi
    echo ""

    # 检查基础域名证书
    echo "基础域名: $base_domain"
    local base_cert="$CERT_PATH/base/fullchain.pem"
    check_domain_certificate_status "$base_domain" "$base_cert"
    echo ""

    # 检查Matrix域名证书
    echo "Matrix域名: $matrix_domain"
    local matrix_cert="$CERT_PATH/matrix/fullchain.pem"
    check_domain_certificate_status "$matrix_domain" "$matrix_cert"
    echo ""

    # 检查TURN域名证书
    echo "TURN域名: $turn_domain"
    local turn_cert="$CERT_PATH/turn/fullchain.pem"
    check_domain_certificate_status "$turn_domain" "$turn_cert"
    echo "=================================="
}

# 检查单个域名证书状态
check_domain_certificate_status() {
    local domain="$1"
    local cert_file="$2"

    # 检查acme.sh状态
    if check_acme_certificate_status "$domain"; then
        echo "  acme.sh状态: ✓ 良好"
    else
        echo "  acme.sh状态: ⚠ 需要检查"
    fi

    # 检查证书文件
    if [[ -f "$cert_file" ]]; then
        # 检查是否为软链接
        if [[ -L "$cert_file" ]]; then
            local target=$(readlink "$cert_file")
            echo "  文件类型: 🔗 软链接 -> $target"

            # 检查软链接目标是否存在
            if [[ -f "$target" ]]; then
                echo "  链接状态: ✓ 目标文件存在"
            else
                echo "  链接状态: ✗ 目标文件不存在"
            fi
        else
            echo "  文件类型: 📄 常规文件"
        fi

        check_certificate_validity "$domain" "$cert_file" >/dev/null 2>&1
        local status=$?
        case $status in
            0) echo "  文件状态: ✓ 有效" ;;
            1) echo "  文件状态: ✗ 无效或即将过期" ;;
            2) echo "  文件状态: ⚠ 需要续期" ;;
        esac

        # 显示证书详细信息
        local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate 2>/dev/null | cut -d= -f2)
        if [[ -n "$expiry_date" ]]; then
            echo "  过期时间: $expiry_date"
        fi

        # 显示证书目录中的其他文件
        local cert_dir="$(dirname "$cert_file")"
        local symlink_count=$(find "$cert_dir" -type l 2>/dev/null | wc -l)
        local regular_count=$(find "$cert_dir" -type f 2>/dev/null | wc -l)
        echo "  目录统计: $symlink_count 个软链接, $regular_count 个常规文件"
    else
        echo "  文件状态: ✗ 证书文件不存在"
        echo "  路径: $cert_file"
    fi
}

# 重新加载服务
reload_services() {
    log_info "重新加载相关服务..."

    # 尝试重新加载nginx
    if systemctl is-active --quiet nginx; then
        systemctl reload nginx
        log_info "Nginx重新加载成功"
    elif [[ -f "$INSTALL_PATH/docker-compose.yml" ]]; then
        cd "$INSTALL_PATH"
        docker compose restart nginx
        log_info "Docker Nginx重新启动成功"
    else
        log_warn "未找到Nginx服务"
    fi
}

# 验证证书路径配置
verify_certificate_paths() {
    log_info "验证证书路径配置..."

    echo "=================================="
    echo "证书路径配置信息"
    echo "=================================="
    echo "ACME_HOME: $ACME_HOME"
    echo "CERT_PATH: $CERT_PATH"
    echo "INSTALL_PATH: $INSTALL_PATH"
    echo ""

    # 检查acme.sh
    if [[ -f "$ACME_HOME/acme.sh" ]]; then
        echo "✓ acme.sh可执行文件: $ACME_HOME/acme.sh"
        local version=$("$ACME_HOME/acme.sh" --version 2>/dev/null | head -1)
        echo "  版本: ${version:-未知}"
    else
        echo "✗ acme.sh可执行文件不存在: $ACME_HOME/acme.sh"
    fi

    # 检查证书目录
    if [[ -d "$CERT_PATH" ]]; then
        echo "✓ 证书目录存在: $CERT_PATH"
        echo "  子目录: $(ls -1 "$CERT_PATH" 2>/dev/null | tr '\n' ' ')"
    else
        echo "✗ 证书目录不存在: $CERT_PATH"
    fi

    # 检查acme.sh证书存储
    local acme_cert_dir="$ACME_HOME"
    if [[ -d "$acme_cert_dir" ]]; then
        echo "✓ acme.sh证书存储: $acme_cert_dir"
        local cert_count=$(find "$acme_cert_dir" -name "*.cer" 2>/dev/null | wc -l)
        echo "  证书文件数量: $cert_count"
    else
        echo "✗ acme.sh证书存储不存在: $acme_cert_dir"
    fi

    echo "=================================="
}

# 显示使用说明
show_usage() {
    echo "Matrix Synapse 智能证书管理脚本 v2.0.0"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  smart <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 智能管理所有域名证书 (推荐，支持软链接)"
    echo "  status <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 检查所有证书状态"
    echo "  force <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 强制重新申请所有证书"
    echo "  create-symlinks <base_domain> <matrix_domain> <turn_domain>"
    echo "                    - 为现有证书创建软链接"
    echo "  renew             - 自动续期即将过期的证书"
    echo "  verify-paths      - 验证证书路径配置"
    echo "  help              - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  CF_TOKEN          - Cloudflare API Token (必需)"
    echo "  INSTALL_PATH      - Matrix安装路径 (默认: /opt/matrix)"
    echo "  CERT_EMAIL        - 证书邮箱地址"
    echo ""
    echo "示例:"
    echo "  # 智能管理证书 (检查有效期，仅在需要时申请)"
    echo "  $0 smart example.com matrix.example.com turn.example.com"
    echo ""
    echo "  # 检查证书状态"
    echo "  $0 status example.com matrix.example.com turn.example.com"
    echo ""
    echo "  # 强制重新申请所有证书"
    echo "  $0 force example.com matrix.example.com turn.example.com"
    echo ""
    echo "  # 自动续期 (通常用于cron任务)"
    echo "  $0 renew"
    echo ""
    echo "Let's Encrypt 配额保护:"
    echo "  • 每周最多5次重复证书申请"
    echo "  • 智能模式会检查证书有效期，避免不必要的申请"
    echo "  • 证书剩余有效期超过30天时不会重新申请"
    echo "  • 证书剩余有效期少于7天时会强制重新申请"
}

# 自动续期所有证书
auto_renew_all() {
    log_info "执行自动续期检查..."

    # 使用acme.sh的自动续期功能
    if "$ACME_HOME/acme.sh" --cron --home "$ACME_HOME"; then
        log_info "自动续期检查完成"
        reload_services
        return 0
    else
        log_error "自动续期检查失败"
        return 1
    fi
}

# 主函数
main() {
    local command="${1:-help}"

    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"

    case "$command" in
        "smart")
            if [[ $# -lt 4 ]]; then
                log_error "smart命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            check_dependencies
            setup_environment

            if smart_certificate_management "$base_domain" "$matrix_domain" "$turn_domain" false; then
                log_info "智能证书管理完成"
                reload_services
                exit 0
            else
                log_error "智能证书管理失败"
                exit 1
            fi
            ;;

        "status")
            if [[ $# -lt 4 ]]; then
                log_error "status命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            check_all_certificates "$base_domain" "$matrix_domain" "$turn_domain"
            ;;

        "force")
            if [[ $# -lt 4 ]]; then
                log_error "force命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            log_warn "强制重新申请所有证书，这将消耗Let's Encrypt配额"
            read -p "确认继续? (y/N): " confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                check_dependencies
                setup_environment
                smart_certificate_management "$base_domain" "$matrix_domain" "$turn_domain" true
                reload_services
            else
                log_info "操作已取消"
            fi
            ;;

        "renew")
            check_dependencies
            setup_environment
            auto_renew_all
            ;;

        "verify-paths")
            verify_certificate_paths
            ;;

        "create-symlinks")
            if [[ $# -lt 4 ]]; then
                log_error "create-symlinks命令需要3个域名参数"
                show_usage
                exit 1
            fi

            local base_domain="$2"
            local matrix_domain="$3"
            local turn_domain="$4"

            log_info "为所有域名创建证书软链接..."
            local success_count=0

            # 为每个域名创建软链接
            for domain in "$base_domain" "$matrix_domain" "$turn_domain"; do
                local cert_subdir
                case "$domain" in
                    "$base_domain") cert_subdir="base" ;;
                    "$matrix_domain") cert_subdir="matrix" ;;
                    "$turn_domain") cert_subdir="turn" ;;
                esac

                local cert_dir="$CERT_PATH/$cert_subdir"
                if ensure_certificate_installed "$domain" "$cert_dir" true; then
                    ((success_count++))
                    log_info "✓ $domain 软链接创建成功"
                else
                    log_error "✗ $domain 软链接创建失败"
                fi
            done

            log_info "软链接创建完成: $success_count/3 成功"
            ;;

        "help"|"-h"|"--help")
            show_usage
            ;;

        *)
            log_error "未知命令: $command"
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi






